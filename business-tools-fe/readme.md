# Business Tools Frontend sub-module

This is a readme document for the Business Tools front-end code.

## Structure

### source code

Frontend code lives in folder ```src```, specifically in
```src/main/webpack/dhl-business-tools```. Child folder names should be self-explanatory (css =
css files, js = javascript files, resources = assets). There is one file that is very important
here though:

#### main.js

File ```main.js``` serves as the 'entry' point for the build system being used here - webpack.
As such, it's a javascript files that imports other files - in essence it's purpose is to
include all files that webpack should process.

> why does it not include the font file from resource folder?

That's because webpack is primarily a 'bundler', meaning it bundles the files together based on
its configuration. Font file doesn't need to be bundled, that's why it's not imported in the
`main.js` file

### tests

There are no javascript tests yet, however, they are expected to live in the `test` folder (when
they are written).

## Build tools machinery

There are several important files to mention here. Following list is unsorted.

### webpack.common.js

This is the main configuration file for webpack, in short - it defines both webpack plugins
(such as Banner plugin to add name & version to the resulting files) and loaders (to separate
css files from javascript files and more). In other words it controls how webpack bundles the
files together.

### webpack.prod.js

This file may eventually be removed - in original AEM archetype maven project, there are more
webpack files whose main difference is whether to minify the resulting js & css code or not.
Since Business tools are only accessed via author, minification is probably not very important.
Nevertheless, for now it's here :)

### clientlib.config.js

Configuration file for aem-clientlib-generator, npm module that builds the AEM clientlibs and
copies them to a specific location.

Following code snippet configures the location where the module will copy the resulting
clientlib files (if you're observant, you may notice it's currently configured to copy the files
to platform-ui and if you're very perceptive you may see the final clientlib location has
changed from /etc to /apps)

```
const CLIENTLIB_DIR = path.join(
    __dirname,
    '..',
    'platform-ui',
    'src',
    'main',
    'content',
    'jcr_root',
    'apps',
    'dhl-business-tools',
    'clientlibs',
);
```

And this javascript object tells the module how to handle various files in scope:

```
module.exports = {
  // context is set to 'current' folder
  context: BUILD_DIR,
  // path to the clientlib root folder (output)
  clientLibRoot: CLIENTLIB_DIR,
  libs: [
    {
      name: "dhl-business-tools",
      categories: ['etc.dhl-business-tools'],
      allowProxy: true,
      serializationFormat: 'xml',
      assets: {
        js: {
          cwd: 'dist/clientlib-site',
          files: ['*.js'],
          flatten: false
        },
        css: {
          cwd: 'dist/clientlib-site',
          files: ['*.css'],
          flatten: false
        },
        resources: {
          cwd: 'dist/clientlib-site',
          files: ['*.woff2'],
          flatten: false,
        },
      }
    }
  ]
}
```

Configuration options are documented
here: https://github.com/wcm-io-frontend/aem-clientlib-generator, but very important one
is ```allowProxy``` which
enables the clientlibs to reside in /apps (recommended) instead of in /etc (NOT recommended)

## How to build

There are two possible ways how to build the Business tools frontend

### NPM

To build via npm, simply navigate to folder with package.json and run

```
npm run prod
```

This will run the 'prod' script from package.json which first executes the webpack to bundle all
the various files together, store them in ```dist``` folder and clientlib-generator build the
clientlib file in platform-ui.

### Maven

Project's pom.xml has been configured to use ```frontend-maven-plugin``` that's capable of
managing your node & npm dependencies inside maven. Therefore, you should just need to run

```
mvn clean install
```

### Building the whole project

Since this module (business tools fe) is part of platform, you can simply build the platform

```
mvn clean install -f <path to platform pom>
```

If you wish to build just the ```platform-wrapper``` or even the ```all```, you already need
this business-tools-fe either in artifactory or in your local maven cache folder.

## Things to improve

* webpack config is probably unnecessarily complex
* aem-clientlib-generator can be called as script directly in webpack
* package.json does not yet contain any 'test' script to execute the unit tests that are going
  to be part of this module
