import {exportObjects as general} from "./general";

function showMassUpdateResult(unparsedJSONString) {
  let parsedJSONString = JSON.parse(unparsedJSONString);
  let HTMLString = "";
  HTMLString += '<div class="well-x-large dhl-color-yellow-opaque round">';

  if ("koUpdates" in parsedJSONString) {
    let koTableColumns = [
      "#",
      "Content path",
      "Property name",
      "Property value",
      "Input file row",
      "Error message",
    ];

    HTMLString += '<div class="flex-child-generic">';
    HTMLString += "<h3>Unsuccessful Updates</h3>";
    HTMLString += '<table class="results">';
    HTMLString += general.buildTableHeader(koTableColumns);
    let counter = 0;
    for (let record of parsedJSONString["koUpdates"]) {
      let koContentPath = record["urlPath"];
      let koPropertyName = record["propertyName"];
      let koPropertyValue = record["propertyValue"];
      let koInputFileRow = record["row"];
      let koErrorMessage = record["errorMessage"];
      let koRowNo = counter + 1;

      HTMLString += "<tr>";
      HTMLString += "<td>" + koRowNo + "</td>";
      HTMLString += "<td>" + koContentPath + "</td>";
      HTMLString += "<td>" + koPropertyName + "</td>";
      HTMLString += "<td>" + koPropertyValue + "</td>";
      HTMLString += "<td>" + koInputFileRow + "</td>";
      HTMLString += "<td>" + koErrorMessage + "</td>";
      HTMLString += "</tr>";
    }
    HTMLString += "</tbody>";
    HTMLString += "</table>";
    HTMLString += "</div>";
  }

  if ("okUpdates" in parsedJSONString) {
    let okTableColumns = [
      "#",
      "Content path",
      "Property name",
      "Property value",
      "Input file row",
    ];

    HTMLString += '<div class="flex-child-generic">';
    HTMLString += "<h3>Successful Updates</h3>";
    HTMLString += '<table class="results">';
    HTMLString += general.buildTableHeader(okTableColumns);
    HTMLString += "<tbody>";
    let okCounter = 0;
    for (let entry of parsedJSONString["okUpdates"]) {
      let okRecord = entry;
      let okContentPath = okRecord["urlPath"];
      let okPropertyName = okRecord["propertyName"];
      let okPropertyValue = okRecord["propertyValue"];
      let okInputFileRow = okRecord["row"];
      let okRowNo = okCounter + 1;

      HTMLString += "<tr>";
      HTMLString += "<td>" + okRowNo + "</td>";
      HTMLString += "<td>" + okContentPath + "</td>";
      HTMLString += "<td>" + okPropertyName + "</td>";
      HTMLString += "<td>" + okPropertyValue + "</td>";
      HTMLString += "<td>" + okInputFileRow + "</td>";
      HTMLString += "</tr>";
    }
    HTMLString += "</tbody>";
    HTMLString += "</table>";
    HTMLString += "</div>";
  }

  HTMLString += "</div>";
  return HTMLString;
}

function massUpdate(form) {
  general.submitForm(
      "/apps/dhl-business-tools/servlet-handler/mass-update.update.html",
      form,
      "massUpdateResult",
      false,
      showMassUpdateResult
  );
}

const exportedFunctions = {
  showMassUpdateResult,
  massUpdate
}

export {exportedFunctions}
