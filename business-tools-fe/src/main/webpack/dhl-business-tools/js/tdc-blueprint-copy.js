import {exportObjects as general} from "./general";

function copyTDCRepos(form) {
  general.submitForm(
      "/apps/dhl-business-tools/servlet-handler/tdc.blueprint-data-copy.html",
      form,
      "ajaxResponse",
      false,
      showTDCCopyResults
  );
}

function getTDCTenants(tdcRepoSearchSubmitId) {
  const servletUrl = "/apps/dhl-business-tools/servlet-handler/tdc.tdc-tenant-finder.html";

  general.fetchGET(servletUrl).then((json) => {
    populateTDCTenantDropdown(json, tdcRepoSearchSubmitId);
  });
}

function populateTDCTenantDropdown(unparsedJson, tdcRepoSearchSubmitId) {
  let allOptions;
  allOptions = `
        <option class="text-center" value="dummy" selected> --- Select TDC Tenant --- </option>
    `
  let optionsString = "";
  const parsedJSON = JSON.parse(JSON.stringify(unparsedJson));
  const TDCDropdownSelectId = document.getElementById("tdc-tenant-dropdown");
  const submitButton = document.getElementById(tdcRepoSearchSubmitId);
  if (submitButton) {
    submitButton.disabled = true;
  }

  if (parsedJSON["tdc-tenants"] && parsedJSON["tdc-tenants"].length !== 0) {
    const tenants = parsedJSON["tdc-tenants"];
    for (let record of tenants) {
      let tenantCode = record["tenant"];
      optionsString = `
                <option class="text-center" value="${tenantCode}">tenant: ${tenantCode.toUpperCase()}</option>
            `
      allOptions = allOptions + optionsString;
    }
  } else {
    allOptions = `
            <option class="text-center" value="">NO TENANT FOUND</option>
        `
  }

  TDCDropdownSelectId.innerHTML = allOptions;
}

/**
 * @param {string} unparsedJSON
 */
function showTDCCopyResults(unparsedJSON) {
  const parsedJSON = JSON.parse(unparsedJSON);
  let HTMLString;
  HTMLString = "";
  HTMLString += `<ul class="text-small dhl-color-text-dark-gray">`;
  let successful = 0;
  let unsuccessful = 0;
  for (let record of parsedJSON) {
    let status = record.status;
    if (status === "OK") {
      successful = successful + 1;
    } else {
      unsuccessful = unsuccessful + 1;
    }
  }
  HTMLString += `
        <li>Successful copies: ${successful} / ${parsedJSON.length}</li>
        <li>Unsuccessful copies: ${unsuccessful} / ${parsedJSON.length}</li>
    `
  return HTMLString;
}

function getTDCRepos() {
  let tenantKey = "tdcTenant";
  let tenantSelect = document.getElementById("tdc-tenant-dropdown");
  let tenantValue = tenantSelect.value || "dhl";

  let tenantIdentifierField = document.getElementById("tdcTenant")
  if (tenantIdentifierField) {
    tenantIdentifierField.value = tenantValue
  }

  const formData = new FormData();
  formData.append(tenantKey, tenantValue);

  general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/tdc.tdc-repo-finder.html",
        "POST",
        formData,
        csrftoken).then(jsonResponse => {
          processTDCRepos(jsonResponse, 'ajaxResponse');
    })
  });
}

function processTDCRepos(jsonResponse, responseDestination) {
  general.showHideElement("tdcRepoCheckboxes", "invisible", "show");
  const blueprintLocationElement = document.getElementById("blueprintLocation")

  const tdcRepos = jsonResponse["tdcRepositories"];
  const blueprintLocation = jsonResponse["blueprintPath"];

  if (blueprintLocationElement) {
    blueprintLocationElement.textContent = "Blueprint: " + blueprintLocation;
  }

  let HTMLString = "";
  let counter = 0;

  for (let record of tdcRepos) {
    HTMLString += '<div class="livecopy-checkbox flex-container text-small">';
    HTMLString += '<div class="flex-child-generic">';
    HTMLString += `#${++counter}`;
    HTMLString += "</div>";
    HTMLString += '<div class="flex-child-generic">';
    HTMLString += '<input class="input-control" value="' + record["tenant"]
        + '/' + record["name"] + '" id="' + record["name"] +
        '" type="checkbox"'
        +
        ' name="tdcReposToUpdate"/>';
    HTMLString += "</div>";
    HTMLString += '<div class="flex-child-generic4">';
    HTMLString += '<label class="left-padding" for="' + record["name"] + '">'
        + record.path + '</label>';
    HTMLString += "</div>";
    HTMLString += '<div class="flex-child-generic2">';
    HTMLString += '<label class="left-padding" for="' + record["name"]
        + '"><strong>' + record["countryName"] + "</strong></label>";
    HTMLString += "</div>";
    HTMLString += "</div>";
  }
  let responseElement = document.getElementById(responseDestination);
  if (responseElement){
    responseElement.innerHTML = HTMLString;
    responseElement.querySelectorAll('input[type="checkbox"][name="tdcReposToUpdate"]').forEach((checkbox) => {
      checkbox.addEventListener("change", () => {
        general.getNumOfChecked("tdcReposToUpdate", "submit-button", "Copy blueprint");
      })
    })
  }


}

const exportedFunctions = {
  copyTDCRepos,
  getTDCRepos,
  getTDCTenants,
  populateTDCTenantDropdown,
  processTDCRepos,
  showTDCCopyResults
}

export {exportedFunctions};
