import { exportObjects as general } from "../general";

function processValidationResults(validationResults, invalidResultsTableId) {
  let table = "";
  let tableInvalid;
  let tableWarnings;

  const validationErrorsMessage = document.getElementById("validationErrorsMessage");
  const validationErrorTable = document.getElementById("validation-error-table");
  document.querySelector('#validation-results-table td.valid').innerHTML = validationResults.numberOfValidRecords
  document.querySelector('#validation-results-table td.invalid').innerHTML = validationResults.numberOfInvalidRecords
  document.querySelector("textarea#parsedRules").innerHTML = JSON.stringify(validationResults);

  if (!validationResults["isCorrect"] || hasWarning(validationResults)) {
    tableInvalid = table + getInvalidRecords(validationResults, table);
    tableWarnings = table + getRecordsWithWarning(validationResults, table);
    table = tableInvalid + tableWarnings;

    validationErrorsMessage.style.display = "block";
    validationErrorTable.style.display = "table";
    document.getElementById(invalidResultsTableId).innerHTML = table;
  }
}

function getRecordsWithWarning(validationResults, table) {
  let formattedTable;
  let warningList;
  let counter = 0;

  for (let redirectRule of validationResults["validRecords"]) {
    if (counter > 9) {
      formattedTable = `
        <tr class="table-row">
          <td class="table-column text-center" colspan="5">We only show the first 10 warnings</td>
        </tr>
        `;
      table = table + formattedTable;
      break;
    }

    if (redirectRule["hasWarning"]) {
      warningList = general.getListFromArray(redirectRule["warningReasons"]);
      formattedTable = `
        <tr class="table-row results">
            <td class="table-column">${redirectRule["ruleName"]}</td>
            <td class="table-column">${redirectRule["rowNumber"]}</td>
            <td class="table-column">WARNING</td>
            <td class="table-column">${warningList}</td>
            <td class="table-column">${redirectRule["sheetName"]}</td>
        </tr>
        `;
      table = table + formattedTable;
      counter = counter + 1;
    }
  }
  return table;
}

function getInvalidRecords(validationResults, table) {
  let formattedTable;
  let errorList;
  let counter = 0;

  if (validationResults["invalidRecords"]?.length == 0 && validationResults["validRecords"]?.length == 0) {
    formattedTable = `
    <tr class="table-row">
      <td class="table-column text-center" colspan="5">No usable data found in the spreadsheet</td>
    </tr>
    `;
    table = table + formattedTable;
  } else {
    for (let redirectRule of validationResults["invalidRecords"]) {
      if (counter > 9) {
        formattedTable = `
          <tr class="table-row">
            <td class="table-column text-center" colspan="5">We only show the first 10 invalid records</td>
          </tr>
          `;
        table = table + formattedTable;
        break;
      }
      errorList = general.getListFromArray(redirectRule["errors"]);
      formattedTable = `
        <tr class="table-row results">
            <td class="table-column">${redirectRule['ruleName']}</td>
            <td class="table-column">${redirectRule['rowNumber']}</td>
            <td class="table-column">ERROR</td>
            <td class="table-column">${errorList}</td>
            <td class="table-column">${redirectRule['sheetName']}</td>
        </tr>
        `;
      table = table + formattedTable;
      counter = counter + 1;
    }
  }
  return table;
}

/**
 * Simple method to provide the information whether we have any records with warning
 * @param validationResults is a string representing the json response returned by AEM
 * @returns {boolean} {@code true} if we found warning in any of the 'valid' records, otherwise {@code false} is returned
 */
function hasWarning(validationResults) {
  for (let validRecord of validationResults["validRecords"]) {
    if (validRecord["hasWarning"]) {
      return true;
    }
  }
  return false;
}

async function validateRedirects(form) {
  const formData = new FormData(form);

  for (const elem of ["validationErrorsMessage", "validation-error-table", "uploadRedirects", "uploadRedirectsHeading"]) {
    document.getElementById(elem).style.display = "none";
  }

  const response = await general.submitFormFetch(
    "/apps/dhl-business-tools/servlet-handler/akamai.validate-input.html",
    "POST",
    formData,
    false
  )
  const uploadRedirects = document.getElementById("uploadRedirects");
  const uploadHeading = document.getElementById("uploadRedirectsHeading");
  uploadRedirects.style.display = "flex";
  uploadHeading.style.display = "block";
  processValidationResults(response, "invalid-results");
}

function showSingleUpload(JSONResponse) {
  const parentElement = document.getElementById("upload-results-information");
  let submissionInfo = general.createTable("results", "redirect-submission")
  parentElement.appendChild(submissionInfo);

  const singleUploadTableColumns = new Map();
  singleUploadTableColumns.set("Property", "Property");
  singleUploadTableColumns.set("Value", "Value");

  general.addTableHeader(singleUploadTableColumns, submissionInfo);
  general.addTableBodyWithId("uploadTableBody", submissionInfo);
  let tableBody = document.getElementById("uploadTableBody");

  for (const [columnName, jsonValue] of getTableColumns()) {
    let value = JSONResponse["statuses"][0][jsonValue];
    if (general.doesColumnContainDateValue(jsonValue,
      ["lastUpdatedOn", "submittedOn"])) {
      value = new Date(value).toISOString();
    }
    let tableRow = document.createElement("tr");
    tableRow.classList.add("table-row");
    tableRow.classList.add("results");
    tableBody.appendChild(tableRow);
    general.addTableCellWithCss(tableRow, columnName, "text-bold");
    general.addTableCell(tableRow, value);
  }
}

function getTableColumns() {
  const columnsAndLookupValues = new Map();
  columnsAndLookupValues.set("Submitted On", "submittedOn");
  columnsAndLookupValues.set("Submitted By", "submittedBy");
  columnsAndLookupValues.set("Policy Name", "policyName");
  columnsAndLookupValues.set("Policy version", "policyVersion");
  columnsAndLookupValues.set("File name", "fileName");
  columnsAndLookupValues.set("Status last updated on", "lastUpdatedOn");
  columnsAndLookupValues.set("Last Message", "message");
  columnsAndLookupValues.set("Status", "uploadStatus");
  return columnsAndLookupValues;
}

function processShowAllUploads(JSONResponse) {
  const uploadResultsTable = document.getElementById("uploadResult");
  const latestSubmissionsTableParentElement = document.getElementById("latest-submissions");
  const policyTables = document.getElementById("redirect-policies-tables");

  generateLatestSubmissionsTable(getTableColumns(), JSONResponse, latestSubmissionsTableParentElement);
  generatePolicySubmissionsTable(getTableColumns(), JSONResponse, policyTables);

  uploadResultsTable.style.display = "block";
}

/**
 * Helper function that creates HTML tables for each policy contained in the JSON response
 *
 * @param columnsAndLookupValues is a Map that contains mapping between column names and lookup values
 * @param JSONResponse is the JSON response where we look for the data
 * @param parentElement is where we attach the submissions for each policy to
 */
function generatePolicySubmissionsTable(columnsAndLookupValues, JSONResponse,
  parentElement) {
  const policyName = Object.keys(JSONResponse["policies"]);

  if (policyName.length > 0) {
    for (let i = 0; i < policyName.length; i++) {
      let tableBodyId = "tableBody" + i;
      let dataRow = JSONResponse["policies"][policyName[i]];
      let tableElement = document.getElementById(policyName[i]);
      if (!tableElement) {
        let table = general.createTable("results", policyName[i]);
        parentElement.appendChild(table);
        general.addTableCaption(table, policyName[i], "dhl-color-text-gray");
        general.addTableHeader(columnsAndLookupValues, table);
        general.addTableBodyWithId(tableBodyId, table);

      }
      let length = dataRow.length > 5 ? 5 : dataRow.length;
      for (let j = 0; j < length; j++) {
        general.addTableRowWithDates(columnsAndLookupValues, dataRow[j],
          document.getElementById(tableBodyId),
          ["lastUpdatedOn", "submittedOn"]);
      }
      let br = document.createElement("br");
      document.getElementById("redirect-policies-tables").appendChild(br);
    }
  } else {
    let policiesUploadContainer = document.getElementById(
      "policiesUploadContainer");
    if (policiesUploadContainer) {
      policiesUploadContainer.classList.add("invisible");
    }
  }

}

/**
 * Helper function that creates the HTML table that contains the latest submissions from the provided JSON response
 *
 * @param columnsAndLookupValues is a javascript Map containing mapping between column names and lookup values -
 * these lookup values are used to search in JSON response
 * @param JSONResponse is (somewhat not surprisingly)v json
 * @param parentElement is the element in HTML that we attach the new table to
 */
function generateLatestSubmissionsTable(columnsAndLookupValues, JSONResponse,
  parentElement) {
  const latestSubmissionsTable = document.getElementById("latest-submissions-table");
  let latestTableBodyId = "latestRedirectUploads"
  if (!latestSubmissionsTable) {
    let latestSubmissionsTable = general.createTable("results", "latest-submissions-table");
    parentElement.appendChild(latestSubmissionsTable);
    general.addTableHeader(columnsAndLookupValues, latestSubmissionsTable);
    general.addTableBodyWithId(latestTableBodyId, latestSubmissionsTable);
    if (JSONResponse["latestUploads"].length > 0) {
      for (let record of JSONResponse["latestUploads"]) {
        general.addTableRowWithDates(columnsAndLookupValues, record,
          document.getElementById(latestTableBodyId),
          ["lastUpdatedOn", "submittedOn"]);
      }
    } else {
      general.addEmptyDataRow("As soon as any redirects are uploaded, they will appear here",
        document.getElementById(latestTableBodyId),
        columnsAndLookupValues.size)
    }

  }
}

async function updateActivations() {
  await general.fetchGET("/apps/dhl-business-tools/servlet-handler/akamai.activations.html")
  location.reload();
}

async function uploadRedirects(form) {
  const formData = new FormData(form);
  const response = await general.submitFormFetch(
    "/apps/dhl-business-tools/servlet-handler/akamai.submit.html",
    "POST",
    formData,
    true
  )
  const uploadResultsHeading = document.getElementById("uploadResultsHeading");
  uploadResultsHeading.style.display = "block";
  showSingleUpload(response)
}

async function getUploadStatus() {
  const tokenResponse = await general.fetchGET("/libs/granite/csrf/token.json")
  const response = await general.submitFormFetch(
    "/apps/dhl-business-tools/servlet-handler/akamai.review.html",
    "POST",
    null,
    tokenResponse.token
  )
  processShowAllUploads(response)
}

const exportedFunctions = {
  validateRedirects,
  updateActivations,
  uploadRedirects,
  getUploadStatus,
}

export { exportedFunctions }
