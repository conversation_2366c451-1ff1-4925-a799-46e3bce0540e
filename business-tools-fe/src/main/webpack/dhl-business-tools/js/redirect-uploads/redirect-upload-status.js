import { exportObjects as general } from "../general";

function processPlatformData(parsedJSON) {
  let tableRow = "";
  let formattedTable;
  const statusError = document.querySelector(".redirect-status-empty");
  const statusTable = document.getElementById("status-table");
  const statusTableBody = document.getElementById("status-table-body");

  try {
    if (parsedJSON.length < 1) {
      let errorMessage = "Unexpected response from AEM. Try refreshing the"
        + " page and repeat the action. If this error appears again,"
        + " kindly liaise with the support team"
      general.showAlert("ajaxResponse", errorMessage, "dhl-color-red");
    }
    if (parsedJSON["statuses"].length > 0) {
      statusTableBody.innerHTML = "";
      for (let status of parsedJSON["statuses"]) {

        formattedTable = `<tr class="table-row results">
            <td>${status["countryName"]} (${status["countryCode"]})</td>
            <td>${status["policyName"]}</td>
            <td> 
                <a target="_blank" href="redirect-uploaded-rules.html?policyname=${status["policyName"]}&version=${status["version"]}&policyId=${status["policyId"]}">
                    ${status["version"]} (activated: ${status["activationDateTime"]})
                    <em class="gg-external"></em>
                </a>
            </td>
        </tr>`;

        tableRow = tableRow + formattedTable;
      }
      statusTable.style.display = "block";
      statusTableBody.innerHTML = tableRow;
    } else {
      statusError.style.display = "block";
    }
  } catch (e) {
    general.showAlert("ajaxResponse", e, "dhl-color-red");
  }
}

async function getPlatformData(platform, environment) {
  const formData = new FormData();
  formData.set("e", environment);
  formData.set("p", platform);
  general.showHideElement("overlay", "invisible", "show");

  const tokenResponse = await general.fetchGET("/libs/granite/csrf/token.json")
  const response = await general.submitFormFetch(
    "/apps/dhl-business-tools/servlet-handler/akamai.uploaded-redirects.html",
    "POST",
    formData,
    tokenResponse.token
  )
  processPlatformData(response);
  general.showHideElement("overlay", "invisible", "hide");
}

const exportedFunctions = {
  getPlatformData
}

export { exportedFunctions };
