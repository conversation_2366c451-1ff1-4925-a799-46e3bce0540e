import { exportObjects as general } from "../general";

/**
 * Function responsible for parsing the incoming data and populating the table
 * showing the redirect rules for an individual policy from Akamai in table format
 *
 * @param parsedJSON is the json that we break down into individual records that will go to the table
 * @param params is an object with several parameters such as version, policyname & country.
 */
function processRulesDetails(parsedJSON, params) {
  let tableRow = "";
  let formattedTable;
  let utcStartTime;
  let utcEndTime;

  const version = params.get("version");
  const policyName = params.get("policyname");
  const countryName = params.get("country");
  const uploadedRrulesTableBody = document.getElementById("uploaded-rules-table-body");
  const parsedRulesInput = document.getElementById("parsedRules");

  if (Object.hasOwn(parsedJSON, "hasError") && parsedJSON["hasError"]) {
    general.addErrorRowToTable(9, parsedJSO<PERSON>["errorMessage"],
      "uploaded-rules-table-body");
  } else {
    for (let record of parsedJSON["redirectRules"]) {
      utcStartTime = getTime(record, "utcStartTime");
      utcEndTime = getTime(record, "utcEndTime");
      let countryCode = getStringValue(record, "countrycode");
      let regionCode = getStringValue(record, "regioncode");
      let regex = getStringValue(record, "regex");
      let cookie = getStringValue(record, "cookie");

      formattedTable =
        `
          <tr class="results">
          <td>${record.ruleName}</td>
          <td>${utcStartTime}</td>
          <td>${utcEndTime}</td>
          <td>${countryCode}</td>
          <td>${regionCode}</td>
          <td>${record.path}</td>
          <td>${record.resultRedirectURL}</td>
          <td>${record.resultStatusCode}</td>
          <td>${record.useIncomingQueryString}</td>
          <td>${regex}</td>
          <td>${cookie}</td>
          </tr>
        `;

      tableRow = tableRow + formattedTable;
    }

    uploadedRrulesTableBody.innerHTML = tableRow;
  }

  parsedRulesInput.innerHTML = JSON.stringify(parsedJSON);

  document.getElementById("policyVersion").value = version;
  document.getElementById("countryName").value = countryName;
  document.getElementById("policyName").value = policyName;
  document.getElementById(
    "policy-name-text").innerHTML = `(policy name: ${policyName}, version: ${version})`;
}

/**
 * Helper function that extracts the String value from teh json object (if it is present).
 * @param record is the json object where we'll be looking for the value of the key
 * @param key is what we're going to be searching for
 * @return {string|*} is the value associated with the key in the json object, or an empty string if not.
 */
function getStringValue(record, key) {
  if (Object.hasOwn(record, key)) {
    return record[key];
  }
  return "";
}

/**
 * Extracts the start or end time from the json. If the value is missing from the provided json, empty string is
 * returned.
 * @param record is a json that is checked for the presence of property
 * @param fieldName is the name of property we're looking for in the json
 * @return {string} a string that represents the start or end date or an empty one
 */
function getTime(record, fieldName) {
  let startDateTime;
  if (Object.hasOwn(record, fieldName) && record[fieldName] > 0) {
    startDateTime = new Date(record[fieldName] * 1000);
    return startDateTime.toISOString();
  }
  return "";
}

async function getRulesDetails() {
  const params = new URLSearchParams(window.location.search);
  const formData = new FormData();

  if (params.has("policyId") && params.has("version") && params.has("policyname")) {
    formData.set("policyId", params.get("policyId"));
    formData.set("version", params.get("version"));
    formData.set("policyName", params.get("policyname"));

    const tokenResponse = await general.fetchGET("/libs/granite/csrf/token.json")
    const response = await general.submitFormFetch(
      "/apps/dhl-business-tools/servlet-handler/akamai.policy-details.html",
      "POST",
      formData,
      tokenResponse.token
    )
    processRulesDetails(response, params);
  }
}

const exportedFunctions = {
  getRulesDetails
}

export { exportedFunctions };
