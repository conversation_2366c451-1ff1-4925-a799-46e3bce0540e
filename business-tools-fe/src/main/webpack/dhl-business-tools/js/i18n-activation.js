import {exportObjects as general} from "./general";

function displayStringLocales(localeList, stringCheckbox) {
  let content;
  let formattedContent = "";
  const i18nDictionaryLocales = document.getElementById("i18n-locales");
  document.getElementById("activate-selected-locale");
  const selectedString = stringCheckbox.querySelector(".i18n-string-input");
  const isChecked = selectedString.checked;
  const stringName = selectedString.value;

  if (isChecked) {
    const localeListArray = localeList.split(",");

    if (localeListArray.length !== 0) {
      for (const locale of localeListArray) {
        content = `
                    <div id="${locale}" class="i18n-locale-checkboxes">
                        <label class="form-checkbox">
                            <input 
                                class="i18n-locale-input" 
                                type="checkbox" 
                                value="${locale}" 
                                name="key-locale-checkboxes" 
                                />
                            <span class="select-checkbox-span">${locale}</span>
                        </label>
                    </div>`;
        formattedContent = formattedContent + content;
      }

      const checkAllLocale = `
                <div class="i18n-checkall-locale-toggle">
                    <label class="form-checkbox">
                        <input 
                            class="i18n-locale-checkall" 
                            type="checkbox" 
                            value="Select All"
                            name="key-locale-checkboxes-all"
                            />
                        <span class="select-checkbox-span" id="select-all-text">Select All</span>
                    </label>
                </div>
            `;
      i18nDictionaryLocales.innerHTML = `<h3>List of available locale</h3>${checkAllLocale}${formattedContent}`;
      i18nDictionaryLocales.value = stringName;

      toggleUnselectedStrings(stringCheckbox, true);

      let checkAllcheckbox = document.querySelector(".i18n-locale-checkall");
      checkAllcheckbox.addEventListener("click",
          (event) => {
            const checkboxes = document.querySelectorAll(".i18n-locale-input");
            toggleCheckbox(event.target, checkboxes);
          })

      checkAllcheckbox.addEventListener("change", () => {
        general.toggleButton(
            "activate-selected-string",
            "key-locale-checkboxes-all");
      })

      document.querySelectorAll(".i18n-locale-input").forEach(element => {
        element.addEventListener("change", () => {
            general.toggleButton(
                "activate-selected-string",
                "key-locale-checkboxes");
        })
      })

    }
  } else {
    i18nDictionaryLocales.innerHTML = "";
    toggleUnselectedStrings(stringCheckbox, false);
  }
}

function toggleUnselectedStrings(selectedCheckbox, isDisabled) {
  const selectedString = selectedCheckbox.querySelector(
      ".i18n-string-input").value;
  const i18nStringCheckboxes = document.querySelectorAll(
      '.i18n-strings-checkboxes');

  for (const checkboxes of i18nStringCheckboxes) {
    if (!isDisabled) {
      checkboxes.removeAttribute("disabled");
    } else if (checkboxes.id !== selectedString && isDisabled) {
      checkboxes.setAttribute("disabled", "");
    }
  }
}

function displayDictionaryDetails(response) {
  let content;
  let formattedContent = "";
  const i18nDictionaryString = document.getElementById("i18n-strings");
  const i18nDictionaryHeading = document.getElementById(
      "i18n-dictionary-heading");

  i18nDictionaryString.innerHTML = "";

  if (response['translationKeys'].length !== 0) {
    const i18nStrings = response['translationKeys'];
    i18nDictionaryHeading.innerHTML = response['dictionaryName'];

    for (const i18nString of i18nStrings) {
      content = `
                <div id="${i18nString['dictionaryKeyName']}" class="i18n-strings-checkboxes">
                    <label class="form-checkbox i18n-string-checkbox">
                        <input class="i18n-string-input" type="checkbox" value="${i18nString['dictionaryKeyName']}"/>
                        <span class="select-checkbox-span">${i18nString['dictionaryKeyName']}
                        </span>
                    </label>
                    <input type="hidden" class="locale-lists" value="${i18nString['dictionaryLanguagesList']}"/>
                </div>`;
      formattedContent = formattedContent + content;
    }
    i18nDictionaryString.innerHTML = `<h3>List of available strings</h3> ${formattedContent}`;

    showModalBox(".i18n-modal-wrapper");

    const allStringCheckboxes = document.querySelectorAll(
        ".i18n-string-checkbox");

    for (const stringCheckbox of allStringCheckboxes) {
      stringCheckbox.addEventListener("click", () => {
        const localeList = stringCheckbox.parentElement.querySelector(
            ".locale-lists");
        displayStringLocales(localeList.value, stringCheckbox);
      })
    }
  }
}

function displayLocales(response) {
  const i18LocaleHeading = document.getElementById("locale-heading");
  const activateLocalesButton = document.getElementById(
      "activate-selected-locale");
  const container = document.getElementById("locales-container")
  const i18nLocales = response['locales'];

  if (activateLocalesButton) {
    let selected = [];
    activateLocalesButton.addEventListener("click", () => {
      const checked = document.querySelectorAll(
          'input[type="checkbox"][name="locale-checkbox"]:checked');
      selected = Array.from(checked).map(x => x.value);
      activateSelectedLocales(selected);

      for (const checkbox of checked) {
        checkbox.checked = false;
      }
    })
  }

  const columnsOnPage = 4;
  let localesSize = response['locales'].length;
  const localesInColumn = Math.ceil(localesSize / columnsOnPage);
  let formattedContent = "";

  i18LocaleHeading.innerHTML = "Select locale(s) in "
      + response['dictionaryName']
      + " you want to activate"
  formattedContent = formattedContent + splitLocalesToChunks(i18nLocales,
      localesInColumn, "", response['dictionaryName']);

  if (container) {
    container.innerHTML = formattedContent;
    showModalBox(".i18n-locales-modal-wrapper");
  }
}

function displayLocalesActivationResult(jsonResponse, elementId) {
  let activationResultContainer = document.getElementById(elementId);
  let activationStatus = document.getElementById("activations-status");

  if (activationResultContainer && activationStatus) {
    let statusContent = "";
    activationResultContainer.classList.remove("invisible");
    let hasError = jsonResponse["containsErrors"];

    if (!hasError) {
      statusContent = localesActivationResultHandling(jsonResponse,
          statusContent)
    } else {
      statusContent = localesActivationResultErrorHandling(jsonResponse,
          statusContent);
    }
    activationStatus.innerHTML += statusContent;
    let dismissButton = document.getElementById("activation-result-dismissal");
    if (dismissButton) {
      dismissButton.addEventListener("click", () => {
        activationResultContainer.classList.add("invisible");
        activationStatus.innerHTML = "";
      })
    }
  }
}

function localesActivationResultHandling(jsonResponse, statusContent) {
  statusContent = statusContent
      + `<p>Requested locale(s) have been sent for activation:</p>`;
  statusContent = statusContent + `<ul>`;
  let loopContent = "";
  for (const item of jsonResponse["requestedActivations"]) {
    loopContent = loopContent + `<li>${item}</li>`;
  }
  return statusContent + loopContent + `</ul>`;
}

function localesActivationResultErrorHandling(jsonResponse, statusContent) {
  let nonActivatedItems = jsonResponse["nonActivatedItems"];

  statusContent = statusContent + `<p>Errors have been detected during activation. 
Please liaise with the support team (so they can check the application logs)
 or try again later</p>`;
  if (nonActivatedItems) {
    statusContent = statusContent + `<ul>`;
    let loopContent = "";
    for (const item of nonActivatedItems) {
      let path = item["dictionaryPath"];
      loopContent = loopContent + `<li>${path}</li>`;
    }
    statusContent = statusContent + `</ul>`;
  }
  return statusContent;
}

function splitLocalesToChunks(originalLocales, chunkSize, content, dictionary) {
  for (let i = 0; i < originalLocales.length; i += chunkSize) {
    const chunk = originalLocales.slice(i, i + chunkSize);

    content = content + `<div class="flex-child-modal">`
    for (let locale of chunk) {
      content = content + `
                <div id="${locale}" class="i18n-strings-checkboxes">
                    <label class="form-checkbox i18n-string-checkbox">
                        <input class="i18n-string-input" name="locale-checkbox" type="checkbox" value="${dictionary}/${locale}"/>
                        <span class="select-checkbox-span">
                            ${locale}
                        </span>
                    </label>
                </div>`;
    }
    content = content + `</div>`;
  }
  return content;
}

function getDictionaryDetails(eventTarget) {
  const dictionaryPath = eventTarget.value;
  const formData = new FormData();
  const submitButton = document.getElementById("activate-selected-string")

  if (submitButton && !submitButton.disabled) {
    submitButton.disabled = true;
  }

  formData.append('dictionary', dictionaryPath);
  general.fetchGET("/libs/granite/csrf/token.json", eventTarget).then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dictionary-contents.html",
        "POST",
        formData,
        csrftoken
    ).then(response => {
      displayDictionaryDetails(response);
    });
  });
}

function getLocalesInDictionary(eventTarget, dictionaryPath) {
  const formData = new FormData();
  formData.append("dictionary", dictionaryPath)

  general.fetchGET("/libs/granite/csrf/token.json", eventTarget).then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dictionary-locales.html",
        "POST",
        formData,
        csrftoken
    ).then(response => {
      displayLocales(response);
      addEventListeners("change", 'activate-selected-locale','locale-checkbox')
    });
  });
}

function generateI18NTable(parsedJSON) {
  let tableRow = "";
  let formattedTable;
  const i18nDictionaryBody = document.getElementById("i18n-dictionary-body");
  const i18nDictionaryEmpty = document.getElementById("i18n-dictionary-empty");

  i18nDictionaryBody.innerHTML = "";

  if (parsedJSON['dictionaries'].length !== 0) {
    const dict = parsedJSON['dictionaries'];

    let counter = 1;
    for (const dictionaryList of dict) {
      if (dictionaryList['hasPermissions']) {
        const lastActivated = dictionaryList['lastActivated'] === undefined
        || dictionaryList['lastActivated'] === "" ? "Unknown" : new Date(
            dictionaryList['lastActivated']).toLocaleString();
        formattedTable = `
                <tr id="${dictionaryList.path}">
                    <td class="td-checkbox">
                        <label class="form-checkbox">
                            <input class="select-dictionary" type="checkbox" name="i18n-dictionaries" value="${dictionaryList.path}"/>
                            <span class="select-checkbox-span"><span>
                        </label>
                    </td>
                    <td>${dictionaryList['tenant'].toUpperCase()}</td>
                    <td>
                        ${dictionaryList['dictionaryName'].toUpperCase()}
                    </td>
                    <td >${dictionaryList.path}</td>
                    <td  class="activation-date">${lastActivated}</td>
                    <td class="status text-bold" id="${dictionaryList.path}"> </td>
                    <td  class="text-center">
                        <button type="button" name="view-translations" id="view-translations_${counter}" class="view-translations button-submit round" value="${dictionaryList.path}">
                            View Translations
                        </button>
                        <button type="button" name="view-locales" id="view-locales_${counter}" class="view-locales button-green round" value="${dictionaryList.path}">
                            View Locales
                        </button>
                    </td>
                </tr>`;
        tableRow = tableRow + formattedTable;
        counter = counter + 1;
      }
    }

    i18nDictionaryBody.innerHTML = tableRow;

    const viewLocalesElement = document.querySelectorAll(".view-locales");
    viewLocalesElement.forEach(element => {
      element.addEventListener("click", (event) => {
        let dictionaryPath = element.value;
        getLocalesInDictionary(event.target, dictionaryPath);
      })
    })

    const viewTranslationsBtn = document.querySelectorAll(".view-translations");
    viewTranslationsBtn.forEach(element => {
      element.addEventListener("click", (event) => {
        getDictionaryDetails(event.target);
      })
    });
  } else {
    i18nDictionaryEmpty.style.display = "block";
  }
}

function getI18nDictionary() {
  const url = "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dicts-finder.html";

  general.fetchGET(url).then((json) => {
    generateI18NTable(json);
  });
}

function toggleCheckbox(evntTarget, checkboxElement) {

  let checkedValue;
  let buttonText;

  if (evntTarget.value === 'Select All') {
    checkedValue = true;
    buttonText = 'Deselect All';

  } else {
    checkedValue = false;
    buttonText = 'Select All';
  }

  for (const checkbox of checkboxElement) {
    if (checkbox.type === "checkbox") {
      checkbox.checked = checkedValue;
    }
  }

  let spanElement = document.getElementById("select-all-text");
  if (spanElement && spanElement.nodeName === "SPAN"
      && spanElement.classList.contains("select-checkbox-span")) {
    spanElement.innerText = buttonText;
  }
  evntTarget.value = buttonText;

}

function displayActivationResults(result) {
  const activationResultElement = document.querySelector(".activation-result");

  activationResultElement.innerHTML = "";
  activationResultElement.classList.remove("activation-error");

  if (result['containsErrors']) {
    activationResultElement.scrollIntoView(true);
    activationResultElement.innerHTML = `${result['errorMessage']}. Please contact Support Team with the error message in Status field to resolve this issue.`;
    activationResultElement.classList.add("activation-error");

    for (const nonActivatedItem of result['nonActivatedItems']) {
      const path = nonActivatedItem.path;
      const tableRow = document.getElementById(path);
      const statusField = tableRow.querySelector('.status');

      statusField.innerHTML = `Error: ${nonActivatedItem['errorReason']}`;
      statusField.classList.add("activation-error");
    }
  } else {
    activationResultElement.innerHTML = "All selected i18n dictionaries activated sucessfully";
  }

  for (const activatedItem of result['requestedActivations']) {
    const tableRow = document.getElementById(activatedItem);
    const activationDate = tableRow.querySelector(".activation-date");
    const status = tableRow.querySelector(".status");

    activationDate.innerHTML = new Date().toLocaleString();
    status.innerHTML = "Activation Successful";
    status.classList.remove("activation-error");
  }
}

function displayLocaleActivationResults(response) {
  const activationWrapper = document.querySelector(
      ".i18n-activation-modal-wrapper");
  const activationResultContainer = document.querySelector(
      ".i18n-activation-modal-container");
  const activationModalHeading = document.querySelector(
      "#i18n-activation-heading");
  let paragraph = "";

  if (!response['containsErrors']) {
    activationModalHeading.innerHTML = "Activation Successful";
    activationResultContainer.innerHTML = `<h3>All strings activated successfully</h3>`;
  } else {
    const errorMessage = `<h3>${response['errorMessage']}</h3>`;
    activationModalHeading.innerHTML = "Activation Unsuccessful";

    for (const nonActivatedItem of response['nonActivatedItems']) {
      paragraph += `<p>Path: ${nonActivatedItem['dictionaryPath']}, Error: ${nonActivatedItem['errorReason']}</p>`;
    }

    activationResultContainer.innerHTML = `${errorMessage}${paragraph}`;
  }
  activationWrapper.style.display = "block";
}

function activateSelectedLocales(selecteds) {
  const formData = new FormData();
  for (const selected of selecteds) {
    formData.append("pagesToActivate", selected);
  }
  general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dicts-activator.html",
        "POST",
        formData,
        csrftoken
    ).then(response => {
      displayLocalesActivationResult(response, "activations-container")
    });
  });
}

function activateSelectedStrings() {
  const checkboxesI18nLocale = document.querySelectorAll(".i18n-locale-input");
  const checkedStringName = document.querySelector("#i18n-locales").value;
  const i18nDictionaryName = document.querySelector(
      "#i18n-dictionary-heading").innerHTML;
  const formData = new FormData();
  const payload = [];

  for (const i18nLocale of checkboxesI18nLocale) {
    if (i18nLocale.checked) {
      payload.push(
          `${i18nDictionaryName}/${i18nLocale.value}/${checkedStringName}`);
      i18nLocale.checked = false;
    }
  }

  for (const value of payload) {
    formData.append('pagesToActivate', value);
  }

  general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dicts-activator.html",
        "POST",
        formData,
        csrftoken
    ).then(response => {
      displayLocaleActivationResults(response);
    });
  });
}

function activateDictionaries() {
  const activateDictonariesButton = document.getElementById(
      "activate-dictionaries");
  const checkboxes = document.querySelectorAll(".select-dictionary");
  const activationResultElement = document.querySelector(".activation-result");
  const formData = new FormData();
  const payload = [];

  for (const checkbox of checkboxes) {
    if (checkbox.checked) {
      payload.push(checkbox.value);
      checkbox.checked = false;
    }
  }

  if (payload.length === 0) {
    activationResultElement.innerHTML = "Please select at least one dictionary for activation";
    activationResultElement.classList.add("activation-error");
  } else {
    for (const value of payload) {
      formData.append('pagesToActivate', value);
    }

    general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
      return res.token;
    }).then(csrftoken => {
      activateDictonariesButton.setAttribute("disabled", "disabled");
      general.submitFormFetch(
          "/apps/dhl-business-tools/servlet-handler/dictionary-activations.dicts-activator.html",
          "POST",
          formData,
          csrftoken
      ).then(response => {
        activateDictonariesButton.removeAttribute("disabled");
        displayActivationResults(response);
      });
    });
  }
}

function closeModalBox(element) {
  general.uncheckedCheckboxes();
  const modalWrapper = document.querySelector(element);
  const modalActivationWrapper = document.querySelector(
      ".i18n-activation-modal-wrapper");
  const i18nLocales = document.querySelector("#i18n-locales");

  modalWrapper.style.display = 'none';
  modalActivationWrapper.style.display = 'none';
  i18nLocales.innerHTML = "";
}

function showModalBox(cssClass) {
  const modal = document.querySelector(cssClass);
  modal.style.display = 'block';
}

/**
 * Function that finds all checkboxes with the given name and adds event listeners to them
 * @param changeType {string} is the type of event listener we want to set - change, click, etc...if not provided or
 * null,
 * defaults to change
 * @param elementId {string} is the id of the element that should listen for the changes
 * @param checkboxesName {string} is the name of the checkboxes we want to add the event listener to
 */
function addEventListeners(changeType, elementId, checkboxesName) {
  const checkboxes = document.querySelectorAll("input[type='checkbox'][name='" + checkboxesName + "']");
  const element = document.getElementById(elementId);

  if (element && checkboxes) {
    for (const checkbox of checkboxes) {
      checkbox.addEventListener(changeType || "change", () => {
        general.toggleButton(elementId, checkboxesName);
      })
    }
  }
}

const exportedFunctions = {
  activateSelectedStrings,
  getI18nDictionary,
  activateDictionaries,
  closeModalBox,
  toggleCheckbox
}
export {exportedFunctions}
