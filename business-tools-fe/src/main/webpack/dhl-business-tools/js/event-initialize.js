// imports
import {exportedFunctions as analytics} from "./analytics-id-report"
import {exportedFunctions as dictionaryActivations} from "./i18n-activation";
import {
  exportedFunctions as redirects
} from "./redirect-uploads/redirect-uploads";
import {
  exportedFunctions as redirectTools
} from "./redirect-uploads/redirect-upload-rules";
import {
  exportedFunctions as redirectStatus
} from "./redirect-uploads/redirect-upload-status"
import {exportedFunctions as tdc} from "./tdc-blueprint-copy";
import {exportedFunctions as multiPublish} from "./multi-publish-tool";
import {exportedFunctions as reporting} from "./reporting-tools";
import {exportedFunctions as mass} from "./mass-update";
import {exportObjects as general} from "./general";

// business tools parts initializations
window.addEventListener("load", function () {
  initializeAkamaiRedirects();
  initializeTdcBlueprintCopy();
  initializeReportingTool();
  initializeI18nActivations();
  initializeAnalytics();
  initializeMultiPublish();
  initializeMassUpdate();
});

/**
 * Initializes the multi publish tool javascript
 */
function initializeMultiPublish() {
  const formActivatePages = document.getElementById("liveCopiesCheckboxes");
  const formCheckPages = document.getElementById("checkPagesCheckboxes");
  const closeButtonLiveCopies = document.getElementById("close-button1");
  const closeButtonActivations = document.getElementById("close-button2");

  // Multi Publish Tool
  const checkStatusButton = document.getElementById("check-status-button");
  const formGetLiveCopies = document.getElementById("masterPageSubmissionForm");
  const masterPageElement = document.getElementById("masterPage");
  const selectAllCheckboxes = document.getElementById("activate");

  if (formGetLiveCopies) {
    formGetLiveCopies.addEventListener("submit", event => {
      event.preventDefault();
      multiPublish.getLiveCopies(formGetLiveCopies, masterPageElement);
      general.updateButtonText("Activate", "submit-button", 0)
    }, false);
  }

  if (formActivatePages) {
    formActivatePages.addEventListener("submit", event => {
      event.preventDefault();
      multiPublish.activatePages(formActivatePages);
    });
  }

  if (closeButtonLiveCopies) {
    closeButtonLiveCopies.addEventListener("click", () => {
      general.showHideElement("liveCopiesCheckboxes", "invisible", "hide");
    });
  }

  if (closeButtonActivations) {
    closeButtonActivations.addEventListener("click", () => {
      general.showHideElement("checkPagesCheckboxes", "invisible", "hide");
    });
  }

  if (checkStatusButton) {
    checkStatusButton.addEventListener("click", (event) => {
      event.preventDefault();
      multiPublish.checkActivationStatus(formCheckPages);
    });
  }

  if(selectAllCheckboxes) {
    selectAllCheckboxes.addEventListener("change", () => {
      general.toggleCheckboxes(selectAllCheckboxes, "liveCopies")
      general.getNumOfChecked('liveCopies', 'submit-button', 'Activate');
    });
  }
}

/**
 * Initializes mass update tool javascript
 */
function initializeMassUpdate() {
  const formMassUpdate = document.getElementById("massUpdateForm");
  if (formMassUpdate) {
    formMassUpdate.addEventListener("submit", event => {
      event.preventDefault();
      mass.massUpdate(formMassUpdate);
    });
  }
}

/**
 * Initializes the Analytics javascript
 */
function initializeAnalytics() {
  // AnalyticsID report
  const analyticsPath = document.getElementById("analytics-path-input");
  const analyticsJson = document.getElementById("analytics-button-json");
  const analyticsExcel = document.getElementById("analytics-button-excel");
  const analyticsFtp = document.getElementById("analytics-button-ftp");
  const analyticsForm = document.getElementById("get-analytics-report");

  if (analyticsForm) {
    if (analyticsJson && analyticsExcel) {
      analyticsJson.addEventListener("click", () => {
        analytics.getAnalyticsReport("json")
      });
      analyticsExcel.addEventListener("click", () => {
        analytics.getAnalyticsReport("excel")
      });
      analyticsFtp.addEventListener("click", () => {
        analytics.getAnalyticsReport("ftp")
      })
    }
  }

  if (analyticsPath) {
    analyticsPath.addEventListener("input", () => {
      analytics.disableExcelAndFtpButtons()
    })
  }
}

/**
 * Initializes the i18n activation javascript
 */
function initializeI18nActivations() {
  // i18n Activation
  const i18nDictionaryList = document.getElementById("i18n-activation");
  const i18nToggleCheckbox = document.getElementById("toggle-checkbox");
  const i18nActivate = document.getElementById("activate-dictionaries");
  const i18nModalClose = document.querySelector("#i18n-locale-modal-close");
  const i18nActivationModalClose = document.querySelector(
      "#i18n-activation-locale-modal-close");
  const i18nActivateSelectedString = document.querySelector(
      "#activate-selected-string");
  const i18nLocalesModalClose = document.getElementById(
      "i18n-locales-modal-close");

  if (i18nDictionaryList) {
    dictionaryActivations.getI18nDictionary();
  }

  if (i18nToggleCheckbox) {
    i18nToggleCheckbox.addEventListener("click", (event) => {
      const checkboxes = document.querySelectorAll(".select-dictionary");
      dictionaryActivations.toggleCheckbox(event.target, checkboxes);
    })
  }

  if (i18nActivate) {
    i18nActivate.addEventListener("click", () => {
      dictionaryActivations.activateDictionaries();
    })
  }

  if (i18nModalClose) {
    i18nModalClose.addEventListener("click", () => {
      dictionaryActivations.closeModalBox(".i18n-modal-wrapper");
    })
  }

  if (i18nLocalesModalClose) {
    i18nLocalesModalClose.addEventListener("click", () => {
      dictionaryActivations.closeModalBox(".i18n-locales-modal-wrapper");
    })
  }

  if (i18nActivationModalClose) {
    i18nActivationModalClose.addEventListener("click", () => {
      dictionaryActivations.closeModalBox(".i18n-activation-modal-wrapper");
    })
  }

  if (i18nActivateSelectedString) {
    i18nActivateSelectedString.addEventListener("click", () => {
      dictionaryActivations.activateSelectedStrings();
    })
  }
}

/**
 * Initializes the Reporting tool javascript
 */
function initializeReportingTool() {
  // Reporting Tool
  const unrefTenantDropdownElement = document.getElementById(
      "references-tenant-dropdown");
  const unrefCountryDropdownElement = document.getElementById(
      "references-country-dropdown");
  const unrefDownloadReport = document.getElementById(
      "download-unreferenced-pages-report");
  const inheritanceTenantDropdownElement = document.getElementById(
      "inheritance-tenant-dropdown");
  const inheritanceCountryDropdownElement = document.getElementById(
      "inheritance-country-dropdown");
  const inheritanceDownloadReport = document.getElementById(
      "download-broken-inheritance-report");

  if (unrefTenantDropdownElement) {
    const referenceLookupForm = document.getElementById(
        "check-unreferenced-pages");

    reporting.getTenantsDropdown(unrefTenantDropdownElement.id,
        "unreferenced-pages");

    unrefTenantDropdownElement.addEventListener("change", event => {
      const selectedTenant = event.target.value;
      reporting.getCountriesDropdown(selectedTenant,
          unrefCountryDropdownElement.id);
    });

    referenceLookupForm.addEventListener("submit", event => {
      event.preventDefault();
      reporting.getUnreferencedPages();
    });
  }

  if (unrefDownloadReport) {
    unrefDownloadReport.addEventListener("submit", event => {
      event.preventDefault();
      reporting.downloadExcelReport(unrefDownloadReport);
    });
  }

  if (inheritanceTenantDropdownElement) {
    const inheritanceLookupForm = document.getElementById(
        "check-broken-inheritance");

    reporting.getTenantsDropdown(inheritanceTenantDropdownElement.id,
        "broken-inheritance");

    inheritanceTenantDropdownElement.addEventListener("change", event => {
      const selectedTenant = event.target.value;
      reporting.getCountriesDropdown(selectedTenant,
          inheritanceCountryDropdownElement.id);
    });

    inheritanceLookupForm.addEventListener("submit", event => {
      event.preventDefault();
      reporting.getInheritancePages();
    });
  }

  if (inheritanceDownloadReport) {
    inheritanceDownloadReport.addEventListener("submit", event => {
      event.preventDefault();
      reporting.downloadExcelReport(inheritanceDownloadReport);
    });
  }
}

/**
 * Initializes the Akamai redirect javascript code
 */
function initializeAkamaiRedirects() {
  // Redirect Uploads
  const statusLegacy = document.getElementById("status-legacy");
  const statusLogistics = document.getElementById("status-logistics");
  const statusEnvironmentSelector = document.getElementById(
      "environment-selector");
  const formRedirectUpload = document.getElementById("redirectUploadForm");
  const validateRedirectRules = document.getElementById(
      "validateRedirectRules");
  const confirmCheckbox = document.getElementById("confirm-checkbox");
  const uploadReview = document.getElementById("uploadReview");

  if (formRedirectUpload) {
    formRedirectUpload.addEventListener("submit", event => {
      event.preventDefault();
      redirects.uploadRedirects(formRedirectUpload);
    });
  }

  if (validateRedirectRules) {
    validateRedirectRules.addEventListener("submit", event => {
      event.preventDefault();
      redirects.validateRedirects(validateRedirectRules);
    });
  }

  if (document.getElementById("redirect-upload-rules-body")) {
    redirectTools.getRulesDetails();
  }

  if (statusLogistics) {
    statusLogistics.addEventListener("click", () => {
      redirectStatus.getPlatformData("logistics",
          statusEnvironmentSelector.innerHTML);
    });
  }

  if (statusLegacy) {
    statusLegacy.addEventListener("click", () => {
      redirectStatus.getPlatformData("legacy",
          statusEnvironmentSelector.innerHTML);
    });
  }

  if (confirmCheckbox) {
    const submitButton = document.getElementById('button-proceed-activate')

    confirmCheckbox.addEventListener("change", event => {
      if (event.target.checked) {
        submitButton.disabled = false;
      } else {
        submitButton.disabled = true;
      }
    });
  }

  if (uploadReview) {
    redirects.getUploadStatus();
  }
}

/**
 * Initializes the TDC blueprint copy javascript code
 */
function initializeTdcBlueprintCopy() {
  const tdcCopySubmissionForm = document.getElementById("tdcRepoCheckboxes");
  const tdcRepos = document.getElementById("tdc-get-repos");
  const tdcBlueprintCopyDiv = document.getElementById(
      "tdc-blueprint-copy-main-div");
  const tdcTenantsDropDown = document.getElementById("tdc-tenant-dropdown");
  const tdcTenantsSubmit = document.getElementById("tdc-get-repos");
  const selectAllCheckboxes = document.getElementById("select-all-checkbox");

  if (tdcBlueprintCopyDiv) {
    tdc.getTDCTenants("tdc-get-repos");
  }

  if (tdcTenantsDropDown && tdcTenantsSubmit) {
    tdcTenantsDropDown.addEventListener("change", () => {
      tdcTenantsSubmit.disabled = tdcTenantsDropDown.value === "dummy";
    })
  }

  if (tdcCopySubmissionForm) {
    tdcCopySubmissionForm.addEventListener("submit", event => {
      event.preventDefault();
      tdc.copyTDCRepos(tdcCopySubmissionForm);

    });
  }

  if (tdcRepos) {
    tdcRepos.addEventListener("click", () => {
      tdc.getTDCRepos();
    });
  }

  if(selectAllCheckboxes){
    selectAllCheckboxes.addEventListener("change", () => {
      general.toggleCheckboxes(selectAllCheckboxes, "tdcReposToUpdate")
      general.getNumOfChecked('tdcReposToUpdate', 'submit-button', 'Copy blueprint');
    });
  }
}


