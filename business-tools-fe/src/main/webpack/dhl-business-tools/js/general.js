/*jshint esversion: 6 */
const sanitizeString = function (whatToSanitize) {
  let tagsToReplace = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
  };

  return whatToSanitize.toString().replace(/[&<>]/g, function (tag) {
    return tagsToReplace[tag] || tag;
  });
}

/**
 * Builds the string representing the table header where individual {@code th}
 * elements are items from the provided array of table columns
 * @param {array} tableColumns is the array that represents the columns
 * @return {string} HTML string
 */
const buildTableHeader = function (tableColumns) {
  let tableHeader = "";

  tableHeader += "<thead><tr>";

  for (let column of tableColumns) {
    tableHeader += "<th>" + column + "</th>";
  }

  tableHeader += "</tr></thead>";
  return tableHeader;
}

/**
 * showHideElement is capable of adding or removing CSS classes based on input parameters
 * @param  elementId {string} [id of the element we wish to hide
 * or show]
 * @param  className {string} [name of the css class]
 * @param  action {string}   [what action comes as parameter, we expect
 * either 'show' or 'hide']
 */
const showHideElement = function (elementId, className, action) {
  let element = document.getElementById(elementId);
  if (element) {
    if (action === "hide" && !element.classList.contains(className)) {
      element.classList.add(className);
    } else if (action === "show" && element.classList.contains(className)) {
      element.classList.remove(className);
    }
  }
}
/**
 * Provides the number of elements that are checked (in other words if the provided element is of type 'checkboxes') and
 * chains a call to function {@code updateButtonText} that takes care of updating the target element with a text specifying
 * how many checkboxes were in fact checked.
 *
 * @param checkboxName is the name that we'll use to look for checkboxes
 * @param updateField is an ID of the element where we'll update its inner HTML (in theory this can be a number of elements
 * that can have their property innerHTML updated this way)
 * @param elementText is the original text that we'll update; if none is provided we provide 'best effort' text
 */
const getNumOfChecked = function (checkboxName, updateField, elementText) {
  let newText;
  let count = 0;
  const checkboxes = getCheckboxes(checkboxName);

  for (let checkbox of checkboxes) {
    if (checkbox.type === "checkbox" && checkbox.checked === true) {
      count = count + 1;
    }
  }
  if (typeof elementText == "undefined") {
    newText = "Activate (" + count + " pages)";
  } else {
    newText = elementText + " (" + count + " records)";
  }

  updateButtonText(newText, updateField, count);
}

const getCheckboxes = function (checkboxName) {
  const checkboxes = document.getElementsByName(checkboxName);
  return Array.from(checkboxes);
}

const updateButtonText = function (newValue, elementId, count) {
  let element = document.getElementById(elementId);
  if (element) {
    element.innerHTML = newValue;
    element.disabled = count < 1;
  }
}

/**
 * <strong>submitForm</strong> is a method that sends the xmlhttp request to provided URL
 * @param url string representing URL where to send the request
 * @param form Form object representing the form that contains data we need to pass to the servlet
 * @param responseElementId tells the method where to send the data that come back from the processing code
 * @param redirectFlag if present, this is added to form object with 'vanity' key and string 'uploadedFile' as value
 * @param processingFunction is a name of the function that's tasked with processing the data coming back from
 * AEM
 * @returns JSON string
 */
const submitForm = function (url, form, responseElementId, redirectFlag,
    processingFunction) {
  const xhr = new XMLHttpRequest();
  const formData = new FormData(form);

  xhr.addEventListener("load", function () {
    showHideElement("overlay", "invisible", "hide");
    document.getElementById(responseElementId).innerHTML = processingFunction(
        this.responseText);
  });

  xhr.addEventListener("error", function () {
    showHideElement("overlay", "invisible", "hide");
    console.log("Error has occurred while trying to communicate with AEM...");
  });

  showHideElement("overlay", "invisible", "show");
  xhr.open("POST", url, true);
  xhr.send(formData);
}

const submitFormFetch = async function (url, method, form, csrftoken,
    overlay = true) {
  let formData;
  let headers;

  if (csrftoken) {
    headers = new Headers();
    headers.append('CSRF-TOKEN', csrftoken);
  }

  if (form !== null) {
    formData = form
  }

  if (overlay) {
    showHideElement("overlay", "invisible", "show");
  }

  const response = await fetch(url, {
    method: method,
    body: formData || "",
    headers: headers
  });

  if (overlay) {
    showHideElement("overlay", "invisible", "hide");
  }

  if (!response.ok) {
    return Promise.reject("Error communicating with AEM");
  } else {
    return response.json();
  }
}

const fetchGET = async function (url) {
  showHideElement("overlay", "invisible", "show");
  const response = await fetch(url);
  showHideElement("overlay", "invisible", "hide");
  if (!response.ok) {
    console.log("Error has occurred while trying to communicate with AEM...");
  } else {
    return response.json();
  }
}

/**
 * Performs the GET request using the javascript's Fetch API and returns the response as Response.json()
 * @param url is a string representing the URL where we want to send the request
 * @returns {Promise<any>} a promise that resolves with the result of parsing the response body text as JSON.
 */
const fetchGetJson = async function (url) {
  showHideElement("overlay", "invisible", "show");
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json"
    },
  });
  showHideElement("overlay", "invisible", "hide");
  if (!response.ok) {
    console.log("Error has occurred while trying to communicate with AEM...");
  }
  return response.json();
}

/**
 * <strong>sendRequest</strong> is a method that sends the XMLHTTP request to provided URL. It doesn't send any form data.
 * @param url string representing URL where to send the request
 * @param responseElementId is a DOM ID where to store the XMLHTTP response
 * @param processingFunction is a function that handles the processing of the response
 * @param passedFormData
 * @param method
 * sent via the xmlhttp request
 * parameters in the xmlhttp request
 */
const sendRequest = function (url, responseElementId, processingFunction,
    passedFormData, method) {
  let requestMethod = typeof method !== "undefined" ? method : "GET";
  let formData = passedFormData || null;
  let xhr = new XMLHttpRequest();

  xhr.addEventListener("load", function () {
    showHideElement("overlay", "invisible", "hide");
    document.getElementById(responseElementId).innerHTML = processingFunction(
        this.responseText);
  });

  xhr.addEventListener("error", function () {
    showHideElement("overlay", "invisible", "hide");
    console.log("Error has occurred while trying to communicate with AEM...");
  });

  showHideElement("overlay", "invisible", "show");
  xhr.open(requestMethod, sanitizeString(url), true);

  if (formData == null) {
    xhr.send();
  } else {
    xhr.send(formData);
  }
}

const showAlert = function (parentElementId, message, additionalClass) {
  const alertElement = document.createElement("div");
  const alertText = document.createTextNode(message);
  const parentElement = document.getElementById(parentElementId);

  alertElement.appendChild(alertText);
  alertElement.classList.add("alert");
  alertElement.classList.add("round");
  alertElement.classList.add(additionalClass);

  parentElement.appendChild(alertElement);
}

const getFileName = function (data) {
  const splitContent = data.split(";");
  return splitContent[1].split("=")[1];
}
/**
 * Disables the element identified by its ID attribute
 * @param elementId is the ID attribute of the element we want to disable. If the element does not exist, this
 * function does not do anything.
 */
const disableElement = function (elementId) {
  let element = document.getElementById(elementId);
  if (element) {
    element.setAttribute("disabled", "");
  }
}

/**
 * Enables the element identified by its ID attribute
 * @param elementId is the ID attribute of the element we want to enable. If the element does not exist, this
 * function does not do anything.
 */
const enableElement = function (elementId) {
  let element = document.getElementById(elementId)
  if (element) {
    element.removeAttribute("disabled");
  }
}

/**
 * Simple function that creates HTML element with provided parameters
 * @param elementType should represent valid HTML element name (such as 'body' or 'table' or 'div')
 * @param elementClasses represents the class (or classes - if array) that are to be added to the element being created
 * @param elementId is an ID of the element to be created
 * @param parentElementId is an ID of element where the new element is being attached to as a child, if this does not
 * @return element that has been created or {@code null} if creation failed (for whatever reason)
 */
const createHtmlElement = function (elementType, elementClasses, elementId,
    parentElementId) {
  let parentElement = document.getElementById(parentElementId);
  let newElement;
  if (parentElement) {
    newElement = document.createElement(elementType);
    newElement.id = elementId;
    if (Array.isArray(elementClasses)) {
      for (let elementClass of elementClasses) {
        newElement.classList.add(elementClass);
      }
    } else {
      newElement.classList.add(elementClasses)
    }
    parentElement.appendChild(newElement);

  }
  return newElement;
}

/**
 * Disables the elements provided to the function as array of elementIDs
 * @param arrayOfElementIds is an array representing the IDs of the elements we want to disable
 */
const disableElements = function (arrayOfElementIds) {
  if (Array.isArray(arrayOfElementIds)) {
    for (let elementId of arrayOfElementIds) {
      disableElement(elementId);
    }
  }
}

/**
 * Enables the elements provided to the function as array of elementIDs. If any of the elements do not exist, this
 * function ignores them.
 * @param arrayOfElementIds is an array representing the IDs of the elements we wish to enable
 */
const enableElements = function (arrayOfElementIds) {
  if (Array.isArray(arrayOfElementIds)) {
    for (let elementId of arrayOfElementIds) {
      enableElement(elementId);
    }
  }
}

/**
 * Adds an error message to a table identified by elementId of 'table body'
 *
 * @param numberOfColumns is 'how many columns we colspan'
 * @param errorMessage is the text we'll insert into the table body
 * @param tableBodyId is the elementID of the table body (where we insert the error message)
 */
const addErrorRowToTable = function (numberOfColumns, errorMessage,
    tableBodyId) {
  let tableBody = document.getElementById(tableBodyId);
  if (tableBody) {
    tableBody.innerHTML = `
            <tr>
            <td colspan="${numberOfColumns}">${errorMessage}</td>
            </tr>
        `;
  }
}

/**
 * Returns HTML un-ordered list from members of an input array
 * @param inputArray is an array we want to transform to HTML list
 * @return {string} either HTML string or an empty string (in case the provided parameter was NOT an array)
 */
const getListFromArray = function (inputArray) {
  if (Array.isArray(inputArray)) {
    let list = "<ul>";
    for (let input of inputArray) {
      list = list + "<li>" + input + "</li>";
    }
    list = list + "</ul>";
    return list;
  }
  return "";
}

/**
 * Creates HTML table (does not attach the table to any element)
 * @param cssClasses is an array of CSS classes that you would like to add to the table
 * @param id is the ID future table is going to have
 * @return HTMLTableElement new Table element
 */
const createTable = function (cssClasses, id) {
  let table = document.createElement("table");
  table.classList.add(cssClasses);
  table.id = id;
  return table;
}

/**
 * Creates a table caption element and attaches it to provided HTML table
 * @param table is the Table element where we'll be attaching the caption
 * @param captionText is the text we want to attach to the caption
 * @param cssClasses is an array of CSS classes we'd like to add to the table caption
 */
const addTableCaption = function (table, captionText, cssClasses) {
  let caption = document.createElement("caption");
  if (cssClasses !== undefined) {
    caption.classList.add(cssClasses);
  }
  let textContent = captionText.toString().toUpperCase();
  let captionContent = document.createTextNode(textContent);
  caption.appendChild(captionContent);
  table.appendChild(caption);
}

/**
 * Adds a table row to table body
 * @param rowNum is the row counter
 * @param jsonData is the data we want to enter to add to this row
 * @param tableBody is the table body we attach the row to
 * @param tableColumns is a map of tableColumns, we use this array to look for
 * the data in the json string (so
 * they have to match exactly or the resulting value displayed in the table would be 'undefined')
 */
const addTableRow = function (rowNum, jsonData, tableBody, tableColumns) {
  let tableRow = document.createElement("tr");
  tableRow.classList.add("table-row");
  tableRow.classList.add("results");
  tableBody.appendChild(tableRow);

  for (const value of tableColumns.values()) {
    addTableCell(tableRow, jsonData[value])
  }
}

/**
 * Adds a table row to table body that spans the entire width of the table
 * @param text is the text value we want to add to the table row
 * @param tableBody is the body where we attach the row to
 * @param tableColumnsNo is a number that is used to determine how much to span the table cell
 */
const addEmptyDataRow = function (text, tableBody, tableColumnsNo) {
  if (tableColumnsNo > 0){
    let tableRow = document.createElement("tr");
    tableRow.classList.add("table-row");
    tableRow.classList.add("results");
    tableBody.appendChild(tableRow);

    let td = document.createElement("td");
    td.colSpan = tableColumnsNo;
    td.classList.add("text-center");
    td.innerText = text;
    tableRow.appendChild(td);
  }
}

/**
 * Adds a table row to table body
 * @param colsAndValues is the row counter
 * @param jsonData is the data we want to enter to add to this row
 * @param tableBody is the table body we attach the row to (they have to
 * match exactly or the resulting value displayed in the table would be 'undefined')
 * @param dateKeys is an array of columns that we expect to contain date values
 */
const addTableRowWithDates = function (colsAndValues, jsonData, tableBody,
    dateKeys) {
  let tableRow = document.createElement("tr");
  tableRow.classList.add("table-row");
  tableRow.classList.add("results");
  tableBody.appendChild(tableRow);

  for (const [, jsonValue] of colsAndValues) {
    let value = jsonData[jsonValue];
    if (doesColumnContainDateValue(jsonValue, dateKeys)) {
      value = new Date(value).toISOString();
    }
    addTableCell(tableRow, value);
  }
}

/**
 * Function that tries to verify if a value exists in both provided arrays. If one of the 'array' parameters turns
 * out to not be an array, we return false....
 * @param needle is the needle
 * @param haystack is an array of needles
 * @return {*} boolean true or false
 */
const doesColumnContainDateValue = function (needle, haystack) {
  return !!haystack.includes(needle);
}

/**
 * Adds a table cell (html <td> element) to existing table row (html tr element)
 * @param tableRow is an element where we want to add the child cell
 * @param cellContent is what we want to add to the cell
 */
const addTableCell = function (tableRow, cellContent) {
  addTableCellWithCss(tableRow, cellContent)
}

const addTableCellWithCss = function (tableRow, cellContent, cssClasses) {
  let td = document.createElement("td");
  if (cellContent !== null) {
    td.innerText = cellContent;
  }
  tableRow.appendChild(td);
  if (cssClasses && cssClasses.trim().length !== 0) {
    td.classList.add(cssClasses);
  }
}
/**
 * Creates the table body element and attaches it to the table
 * @param tableId is the ID we want to assign to the newly-created element
 * @param table is the table where we add the table body
 */
const addTableBodyWithId = function (tableId, table) {
  let tableBody = document.createElement("tbody");
  tableBody.classList.add("table-body");
  tableBody.id = tableId;
  table.appendChild(tableBody);
}

/**
 * Creates table header based on provided parameters
 * @param columnsMap is a map that contains both 'visual' representation of column name (key) and the actual 'json'
 * lookup value (value) [that is however not used in this function]
 * @param tableElement is the table where we'll attach the thead
 */
const addTableHeader = function (columnsMap, tableElement) {
  // create table header
  let thead = document.createElement("thead");
  thead.classList.add("table-head");
  // append table header to table
  tableElement.appendChild(thead);

  let columnsArray;
  if (columnsMap instanceof Map) {
    columnsArray = columnsMap.keys();
  } else {
    columnsArray = columnsMap;
  }
  for (let key of columnsArray) {
    let th = document.createElement("th");
    th.classList.add("column-header");
    th.scope = "col";
    th.innerText = key;
    thead.appendChild(th);
  }
}

/**
 * Simple function that goes through the current 'document' and finds all checkboxes and unchecks them
 */
const uncheckedCheckboxes = function () {
  const checkBoxes = document.querySelectorAll('input[type="checkbox"]');
  for (const checkbox of checkBoxes) {
    checkbox.checked = false;
  }
}

/**
 * Checks or unchecks all checkboxes sharing the same name depending on the state of the 'source' checkbox (if
 * source checkbox is checked, then all checkboxes of the provided name are checked, otherwise they are unchecked)
 * Useful to implement the 'Select All' / 'Unselect All' functionality
 * @param source {HTMLInputElement} is the checkbox that triggered the event
 * @param checkboxName {string} is the name of the checkboxes
 */
const toggleCheckboxes = function (source, checkboxName) {
  const checkboxes = document.getElementsByName(checkboxName);
  for (const element of checkboxes) {
    if (element.type === "checkbox") {
      element.checked = source.checked;
    }
  }
}

/**
 * (De)Activates the button depending on the number of checked checkboxes. If no checkbox is ticked, button should be
 * disabled. Otherwise, it should be enabled.
 * @param elementId is the 'id' property of a button we want to either enable or disable
 * @param checkboxesName is the name of the checkboxes (it is assumed all checkboxes have the same name, otherwise they
 * cannot be controlled)
 */
function toggleButton(elementId, checkboxesName) {
  let counter = 0;
  const allCheckboxes = document.getElementsByName(checkboxesName);
  for (const checkbox of allCheckboxes) {
    if (checkbox.type === "checkbox" && checkbox.checked === true) {
      counter = counter + 1;
      enableElement(elementId);
    }
  }
  if (counter < 1) {
    disableElement(elementId);
  }
}



const exportObjects = {
  addEmptyDataRow,
  addErrorRowToTable,
  addTableBodyWithId,
  addTableCaption,
  addTableCell,
  addTableCellWithCss,
  addTableHeader,
  addTableRow,
  addTableRowWithDates,
  buildTableHeader,
  createHtmlElement,
  createTable,
  disableElement,
  disableElements,
  doesColumnContainDateValue,
  enableElement,
  enableElements,
  fetchGET,
  fetchGetJson,
  getCheckboxes,
  getFileName,
  getListFromArray,
  getNumOfChecked,
  sanitizeString,
  sendRequest,
  showAlert,
  showHideElement,
  submitForm,
  submitFormFetch,
  toggleButton,
  toggleCheckboxes,
  uncheckedCheckboxes,
  updateButtonText,
}
export {exportObjects};
