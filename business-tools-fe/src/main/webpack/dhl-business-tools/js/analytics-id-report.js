import {exportObjects as general} from "./general";

const analyticsTableBodyId = "analytics-table-body";
const presentedTableRows = 10;

function getAnalyticsReport(outputType) {

  resetElements();
  const errorDiv = document.getElementById("analytics-error-div");
  const analyticsPathInput = document.getElementById("analytics-path-input");
  const countryValue = analyticsPathInput.value;

  let existingTable = document.getElementById("results-table");
  const base = document.getElementById("analytics-report-base");
  if (base && existingTable) {
    base.removeChild(existingTable);
  }

  if (countryValue === "" || !countryValue.startsWith("/content/")) {
    showError(errorDiv,
        "Path field cannot be empty and it must begin with /content/");
  } else {
    const url = `/apps/dhl-business-tools/servlet-handler/analytics.generation.html`;
    let queryParams = new URLSearchParams(
        {"dryRun": "true", "path": countryValue});

    switch (outputType) {
      case "excel":
        queryParams.append("output", "excel");
        getExcelResponse(url, queryParams);
        break;
      case "ftp":
        queryParams.append("output", "ftp");
        getFtpResponse(url, queryParams);
        break;
      default:
        queryParams.append("output", "json");
        getJsonResponse(url, queryParams);
    }
  }
}

function getJsonResponse(path, queryParams,) {
  const url = path + "?" + queryParams.toString();
  general.fetchGetJson(url)
  .then((json) => {
    processAnalyticsIdReport(json);
  });
}

function getFtpResponse(path, queryParams) {
  const url = path + "?" + queryParams.toString();
  general.fetchGetJson(url)
  .then((json) => {
    processAnalyticsIdFtpReport(json);
  })
}

function processAnalyticsIdFtpReport(json) {
  const resultScreen = document.getElementById("results-screen");
  if (resultScreen) {
    resultScreen.classList.remove("invisible");
  }
  document.getElementById("analytics-report-base");
  let cssClasses = ["width-half",
    "flex-container-generic",
    "div-centered",
    "dhl-color-lightest-gray",
    "dhl-box-shadow-lightest-gray",
    "padding-small",
    "dhl-color-text-dark-gray",
    "dhl-border-medium-gray"];
  general.createHtmlElement("div", cssClasses, "ftpResponseDiv",
      "analytics-report-base");
  let cssClassesReportKeys = ["flex-child-info-no-pad", "text-center"];
  let cssClassesReportValues = ["flex-child-info-no-pad", "text-center",
    "text-bold", "padding-small", "dhl-color-text-dark-gray", "text-left"];

  let reportKey = general.createHtmlElement("div", cssClassesReportKeys,
      "ftpResponseKey", "ftpResponseDiv");
  let reportValue = general.createHtmlElement("div", cssClassesReportValues,
      "ftpResponseValue", "ftpResponseDiv");
  let reportKey2 = general.createHtmlElement("div", cssClassesReportKeys,
      "ftpResponseKey", "ftpResponseDiv");
  let reportValue2 = general.createHtmlElement("div", cssClassesReportValues,
      "ftpResponseValue", "ftpResponseDiv");
  let reportKey3 = general.createHtmlElement("div", cssClassesReportKeys,
      "ftpResponseKey", "ftpResponseDiv");
  let reportValue3 = general.createHtmlElement("div", cssClassesReportValues,
      "ftpResponseValue", "ftpResponseDiv");

  reportKey.textContent = "Upload successful:";
  reportValue.textContent = json["ok"];
  if (json["ok"] !== true) {
    reportKey2.textContent = "Error Reason";
    reportValue2.textContent = json["Error"];
  } else {
    reportKey2.textContent = "Uploaded to server:";
    reportValue2.textContent = json["FTP server"];
    reportKey3.textContent = "Filename:";
    reportValue3.textContent = json["Filename"];
  }
}

function getExcelResponse(path, queryParams) {

  let today = new Date();
  let fileName = "analytics-report-" + today.getFullYear() + "-"
      + (today.getMonth() + 1) + "-" + today.getDate() + "-" + today.getHours()
      + today.getMinutes() + today.getSeconds() + ".xlsx";
  let url = path + "?" + queryParams.toString()

  general.showHideElement("overlay", "invisible", "show");
  fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/vnd.ms-excel"
    }
  }).then(data => {
    general.showHideElement("overlay", "invisible", "hide");
    return data.blob();
  }).then(response => {
    const dataType = response.type;
    const binaryData = [];
    binaryData.push(response);
    const downloadLink = document.createElement("a");
    downloadLink.href = window.URL.createObjectURL(
        new Blob(binaryData, {type: dataType}));
    downloadLink.setAttribute('download', fileName);
    document.body.appendChild(downloadLink);
    downloadLink.click();
    downloadLink.remove();
  })
}

function processAnalyticsIdReport(jsonData) {
  document.getElementById("analytics-button-excel");
  document.getElementById("analytics-button-ftp");
  const errorDiv = document.getElementById("analytics-error-div");
  if (errorDiv && !errorDiv.classList.contains("invisible")) {
    errorDiv.classList.add("invisible");
  }
  try {
    if (jsonData["Input"]["invalid"] === true) {
      showError(errorDiv,
          "Invalid path! Provided path is either invalid or does not" +
          " exist. ");
      disableExcelAndFtpButtons();
    }
  } catch (err) {
    if (jsonData["servletRequestParams"]["invalid"] === true) {
      showError(errorDiv,
          "Invalid path! Provided path is either invalid or does not" +
          " exist. ");
      disableExcelAndFtpButtons();
    }
  }

  general.enableElements(["analytics-button-excel", "analytics-button-ftp"])
  showAnalyticsReport(jsonData);
  document.getElementById("results-screen").classList.remove("invisible");
}

function showAnalyticsReport(jsonData) {

  try {
    const base = document.getElementById("analytics-report-base");
    const pages = jsonData["Pages"];
    if (base) {
      let table = general.createTable("results", "results-table");
      base.appendChild(table);

      const tableColumns = ["Externally available URL", "Path",
        "Analytics ID", "Analytics ID set"]
      let upperLoopLimit = pages.length > presentedTableRows
          ? presentedTableRows : pages.length;
      general.addTableHeader(tableColumns, table);
      general.addTableBodyWithId(analyticsTableBodyId, table);
      for (let i = 0; i < upperLoopLimit; i++) {
        general.addTableRow(
            i,
            pages[i],
            document.getElementById(analyticsTableBodyId),
            tableColumns)
      }
      addTableFooter(pages.length, tableColumns.length, table);
    }
  } catch (e) {
    disableExcelAndFtpButtons();
  }
}

function addTableFooter(numberOfPages, numberOfColumns, table) {
  let tableFooter = document.createElement("tfoot")
  let footerRow = document.createElement("tr");
  let footerCell = document.createElement("td")
  footerCell.colSpan = numberOfColumns;
  footerCell.classList.add("text-center");
  footerCell.classList.add("dhl-color-text-gray");
  footerCell.innerText = "There are " + numberOfPages + " pages, we only"
      + " present the first " + presentedTableRows + " of them. To get the" +
      " full" +
      " report, download the data as spreadsheet"
  footerRow.appendChild(footerCell);
  tableFooter.appendChild(footerRow);
  table.appendChild(tableFooter);
}

function showError(errorElement, errorMessage) {
  let errorMessageElement = document.getElementById("errorMessage");
  if (errorElement) {
    errorElement.classList.remove("invisible");
    if (errorMessage !== "") {
      errorMessageElement.textContent = errorMessage;
    } else {
      errorMessageElement.textContent = "Invalid path! Provided path is either invalid or does not"
          +
          " exist. "
    }
  }
}

function resetElements() {
  const errorDiv = document.getElementById("analytics-error-div");
  const ftpReportDiv = document.getElementById("ftpResponseDiv");
  if (ftpReportDiv) {
    ftpReportDiv.remove();
  }

  if (errorDiv && !errorDiv.classList.contains("invisible")) {
    errorDiv.classList.add("invisible");
  }

  const resultScreen = document.getElementById("results-screen");
  if (resultScreen) {
    resultScreen.classList.add("invisible");
  }

  disableExcelAndFtpButtons();
}

/**
 * Disables both 'excel' and 'ftp' buttons in the analytics tool, both buttons have 'hard-coded' values of element IDs.
 */
function disableExcelAndFtpButtons() {
  let elementsToDisable = ["analytics-button-excel", "analytics-button-ftp"];
  general.disableElements(elementsToDisable);
}

const exportedFunctions = {
  disableExcelAndFtpButtons,
  getAnalyticsReport,
}

export {exportedFunctions};
