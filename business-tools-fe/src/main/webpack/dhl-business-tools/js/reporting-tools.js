import {exportObjects as general} from "./general";

let tenantData = [];

function populateDropdown(referenceData, elementId) {
  const dropdownElement = document.getElementById(elementId);

  for (const data of referenceData) {
    if (Object.keys(data).length !== 0) {
      let option;

      if (elementId === "references-tenant-dropdown" || elementId
          === "inheritance-tenant-dropdown") {
        if (data['tenant']) {
          option = document.createElement("option");
          option.textContent = data['tenant'].toUpperCase();
          option.value = data['tenant'];
          dropdownElement.appendChild(option);
        }
      } else {
        option = document.createElement("option");
        option.textContent = data['countryName'].toUpperCase() + " ("
            + data.path + ")";
        option.value = data.path;
        dropdownElement.appendChild(option);
      }
    }
  }
}

function removeDropdownOptions(dropdownElementId) {
  const dropdownOptions = document.querySelectorAll(
      "#" + dropdownElementId + " option");
  dropdownOptions.forEach((el) => {
    if (el.text !== "Select a Country") {
      el.remove();
    }
  });
}

function processReferencePageResult(json) {
  const pageWithoutReference = document.getElementById(
      "reference-orphaned-pages");
  const excludedByConfig = document.getElementById("reference-excluded-pages");
  const totalPageScope = document.getElementById("reference-total-pages");
  const pathNameElement = document.getElementById("reference-path");
  const resultsScreen = document.getElementById("reference-results-screen");
  const hiddenField = document.getElementById("reference-response-data");

  const responseData = JSON.stringify(json);

  totalPageScope.innerHTML = json['totalPagesInScope'];
  pageWithoutReference.innerHTML = json['pagesWithoutReferences'].length;
  excludedByConfig.innerHTML = json['excludedByConfiguration'].length;
  pathNameElement.innerHTML = json['searchPath'];
  hiddenField.value = responseData;

  resultsScreen.style.display = "block";
}

function getUnreferencedPages() {
  const tenant = document.getElementById("references-tenant-dropdown");
  const country = document.getElementById("references-country-dropdown");

  const tenantValue = tenant.options[tenant.selectedIndex].value;
  const countryValue = country.options[country.selectedIndex].value;

  let param;

  if (countryValue === "") {
    param = "/content/" + tenantValue;
  } else {
    param = countryValue;
  }

  const url = `/apps/dhl-business-tools/servlet-handler/reporting.repository-query.html?baseFolder=${param}&reportType=unreferenced-pages`;

  general.fetchGET(url).then((json) => {
    processReferencePageResult(json);
  })
}

function processInheritancePageResult(json) {
  const brokenProperties = document.getElementById(
      "inheritance-properties-broken");
  const brokenComponents = document.getElementById(
      "inheritance-components-broken");
  const addedComponents = document.getElementById(
      "inheritance-components-added");
  const pathNameElement = document.getElementById("inheritance-path");
  const filterPathElement = document.getElementById(
      "inheritance-filtered-parents");
  const resultsScreen = document.getElementById("inheritance-results-screen");
  const hiddenField = document.getElementById("inheritance-response-data");

  const responseData = JSON.stringify(json);

  brokenProperties.innerHTML = json['pagesWithBrokenProperties'];
  brokenComponents.innerHTML = json['pagesWithBrokenComponents'];
  addedComponents.innerHTML = json['pagesWithAddedComponents'];
  pathNameElement.innerHTML = json['searchPath'];
  filterPathElement.innerHTML = (json['filterParent'] ? json['filterParent']
      : "");
  hiddenField.value = responseData;

  resultsScreen.style.display = "block";
}

function getInheritancePages() {
  const tenant = document.getElementById("inheritance-tenant-dropdown");
  const country = document.getElementById("inheritance-country-dropdown");
  const filter = document.getElementById("inheritance-path-filter");

  const tenantValue = tenant.options[tenant.selectedIndex].value;
  const countryValue = country.options[country.selectedIndex].value;
  const filterValue = filter.value;

  let param;

  if (countryValue === "") {
    param = "/content/" + tenantValue;
  } else {
    param = countryValue;
  }

  const url = `/apps/dhl-business-tools/servlet-handler/reporting.repository-query.html?baseFolder=${param}&reportType=broken-inheritance&filter=${filterValue}`;

  general.fetchGET(url).then((json) => {
    processInheritancePageResult(json);
  })
}

async function getTenantsDropdown(dropdownElementId, reportType) {
  const url = `/apps/dhl-business-tools/servlet-handler/reporting.tenants.html?reportType=${reportType}`;
  await general.fetchGET(url).then((json) => {
    tenantData = json;
    populateDropdown(tenantData["tenants"], dropdownElementId);
  })
}

function getCountriesDropdown(tenant, dropdownElementId) {
  const tenantsArray = tenantData["tenants"];

  removeDropdownOptions(dropdownElementId);

  for (const el of tenantsArray) {
    const country = el["countries"];

    if (country.length !== 0 && el["tenant"] === tenant) {
      populateDropdown(country, dropdownElementId);
    }
  }
}

function downloadExcelReport(form) {
  const url = "/apps/dhl-business-tools/servlet-handler/reporting.download.html";
  const formData = new FormData(form);
  let fileName;

  general.showHideElement("overlay", "invisible", "show");

  fetch(url, {
    method: "POST",
    body: formData,
  }).then((response) => {
    general.showHideElement("overlay", "invisible", "hide");
    fileName = general.getFileName(response.headers.get("content-disposition"));
    return response.blob();
  }).then((blob) => URL.createObjectURL(blob)).then((downloadURL) => {
    let anchor = document.createElement("a");
    document.body.appendChild(anchor);
    anchor.style = "display: none";
    anchor.href = downloadURL;
    anchor.download = fileName;
    anchor.click();
    URL.revokeObjectURL(downloadURL);
  }).catch(() => {
    console.log("Error has occurred while trying to communicate with AEM...");
  });
}

const exportedFunctions = {
  downloadExcelReport,
  getCountriesDropdown,
  getInheritancePages,
  getTenantsDropdown,
  getUnreferencedPages,
  populateDropdown,
  processInheritancePageResult,
  processReferencePageResult,
  removeDropdownOptions,
}

export {exportedFunctions};
