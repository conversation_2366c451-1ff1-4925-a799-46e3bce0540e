import {exportObjects as general} from "./general";

function sendErrorMessage(message) {
  const reporter = document.getElementById("notifier");
  reporter.innerHTML = `<i class="gg-danger"></i> ${message} <i class="gg-danger"></i>`;
  reporter.classList.remove("invisible");
}

function showLiveCopies(parsedJSON) {
  let tableRow = "";
  let formattedTable;
  const liveCopiesEmptyElement = document.getElementById("livecopy-empty-data");
  const liveCopiesResultsElement = document.getElementById(
      "livecopy-result-data");

  if (parsedJSON.length < 1) {
    liveCopiesEmptyElement.style.display = "block";
  } else {
    liveCopiesEmptyElement.style.display = "none";

    for (let record of parsedJSON) {
      formattedTable =
          `<div class="livecopy-checkbox flex-container">
                <div class="flex-child-generic">
                </div>
                <div class="flex-child-generic">
                    <input 
                        class="input-control"
                        value="${record.path}"
                        id="${record.path}"
                        type="checkbox"
                        name="liveCopies"
                    />
                </div>
                <div class="flex-child-generic4">
                    <label class="left-padding" for="${record.path}">
                        ${record.path}
                    </label>
                </div>
                <div class="flex-child-generic2">
                    <label class="left-padding" for="${record.path}">
                        <span class="text-bold">${record.language}</span>
                    </label>
                </div>
            </div>`;

      tableRow = tableRow + formattedTable;
    }
    liveCopiesResultsElement.innerHTML = tableRow;

    liveCopiesResultsElement.querySelectorAll('input[type="checkbox"][name="liveCopies"]').forEach((checkbox) => {
      checkbox.addEventListener("change", () => {
        general.getNumOfChecked("liveCopies", "submit-button", "Activate");
      })
    })
  }
}

function showActivationResult(parsedJSON) {
  let tableRow = "";
  let formattedTable;

  const massPublishResultsBody = document.getElementById(
      "mass-publish-results-body");

  let counter = 0;
  for (let record of parsedJSON) {
    counter = counter + 1;

    formattedTable = `
            <tr>
                <td>${counter}</td>
                <td>${record.path}<input name="liveCopies" type="hidden" value="${record.path}"/></td>
                <td>
                    <div class="tooltip">${record["activationStatus"]} 
                        <span class="tooltiptext">${record["activationStatusDetail"]}</span>
                    </div>
                </td>
                <td>${record["activationStatusCheckedOn"]}</td>
            </tr>
        `;

    tableRow = tableRow + formattedTable;
  }

  massPublishResultsBody.innerHTML = tableRow;
}

function activatePages(form) {
  const formData = new FormData(form);

  general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
    return res.token;
  }).then(csrfToken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/multi-publish-handler.activate.html",
        "POST",
        formData,
        csrfToken
    ).then(response => {
      showActivationResult(response);
    });
  })

  general.showHideElement("liveCopiesCheckboxes", "invisible", "hide");
  general.showHideElement("checkPagesCheckboxes", "invisible", "show");
}

function checkActivationStatus(form) {
  const formData = new FormData(form);

  general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
    return res.token;
  }).then(csrftoken => {
    general.submitFormFetch(
        "/apps/dhl-business-tools/servlet-handler/multi-publish-handler.activationStatus.html",
        "POST",
        formData,
        csrftoken
    ).then(response => {
      showActivationResult(response);
    });
  });
}

function getLiveCopies(form, masterPageElement) {
  const formData = new FormData(form);

  if (masterPageElement.value.startsWith("/content/")) {
    general.fetchGET("/libs/granite/csrf/token.json").then((res) => {
      return res.token;
    }).then(csrfToken => {
      general.submitFormFetch(
          "/apps/dhl-business-tools/servlet-handler/multi-publish-handler.livecopy.html",
          "POST",
          formData,
          csrfToken
      ).then(response => {
        general.showHideElement("liveCopiesCheckboxes", "invisible", "show");
        general.showHideElement("checkPagesCheckboxes", "invisible", "hide");
        showLiveCopies(response);
      });
    })

  } else {
    sendErrorMessage(
        "Paths are expected to start with '/content/'. Correct and re-submit.");
  }
}

const exportedFunctions = {
  activatePages,
  checkActivationStatus,
  getLiveCopies,
  sendErrorMessage,
  showActivationResult,
  showLiveCopies,
}

export {exportedFunctions};
