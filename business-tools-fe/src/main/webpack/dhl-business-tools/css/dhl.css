.dhl-color-red{
  background-color: #D40511;
}

.dhl-color-warning{
  background-color: #FFAA2B;
}

.dhl-color-success{
    background-color: #6ABD6E;
}

.dhl-color-yellow {
    background-color: #ffcc00;
}

.dhl-color-yellow-opaque {
    background-color: var(--dhl-color-lightest-yellow);
}

.dhl-color-lightest-gray {
    background-color: var(--dhl-color-lightest-gray);
}

.dhl-color-black {
    background-color: #323232;
}

.dhl-color-green {
    background-color: #76bd22;
}

.dhl-color-dark-gray {
  background-color: #666666
}

.dhl-color-medium-gray{
  background-color: #c8c8c8;
}

.dhl-color-light-gray{
  background-color: #f1f1f1;
}

.dhl-color-text-light-gray{
  color: #f1f1f1;
}

.dhl-color-text-black{
  color: #323232;
}

.dhl-color-text-gray{
    color: var(--main-text-color);
}

.dhl-color-text-red{
  color: #D40511;
}

.dhl-color-text-medium-gray{
  color: #c8c8c8;
}

.dhl-color-text-dark-gray{
    color: var(--dhl-color-dark-gray);
}

.dhl-color-text-green {
    color: var(--dhl-color-text-green);
}

.dhl-color-text-white {
    color: white;
}

.dhl-text-size-small {
    font-size: small;
}

.dhl-text-size-normal {
    font-size: medium;
}

.dhl-header-gradient {
    background: #fc0 linear-gradient(to right, #fc0 0%, #fc0 30%, #ffe57f 79%, #fff0b2 100%) no-repeat;
    background-position-x: 214px;
    background-size: calc(100% - 214px) 100%;
}

.dhl-border-green {
    border: 1px solid #76bd22;
}

.dhl-heading-border {
    border: var(--dhl-heading-border);
}

.dhl-border-medium-gray {
    border: 1px solid var(--dhl-color-dark-gray);
}

.dhl-border-right-none {
    border-right: none;
}

.dhl-border-left-none {
    border-left: none;
}

.dhl-box-shadow-lightest-gray {
    box-shadow: 7px 7px 7px var(--dhl-color-lightest-gray);
}

.left-padding {
    padding-left: 5px;
}

.text-small {
    font-size: smaller;
}

.text-bold
{
  font-weight: bolder;
}

.round{
    border-radius: 4px;
}

.dhl-gray-border {
    border: 1px solid #F1F1F1;
}

.text-center {
    text-align: center;
}

.div-centered {
    margin-left: auto;
    margin-right: auto;
    margin-top: 10px;
}

.padding-small {
    padding: 5px;
}

.table-column {
    border: 1px solid #333;
    padding: 5px;
    font-size: 12px;
}

.table-head {
    background-color: #ffcc00;
    color: #333;
}

.column-header {
    font-size: 14px;
    padding: 5px;
}

.max-width {
    width: 100%;
}

.dhl-opacity {
    opacity: 50%;
}
