.gg-chevron-down {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    transform: scale(var(--ggs, 1));
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-radius: 100px
}

.gg-chevron-down::after {
    content: "";
    display: block;
    box-sizing: border-box;
    position: absolute;
    width: 10px;
    height: 10px;
    border-bottom: 2px solid;
    border-right: 2px solid;
    transform: rotate(45deg);
    left: 7px;
    top: 1px
}

.gg-close {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    transform: scale(var(--ggs, 1));
    width: 22px;
    height: 16px;
    border: 2px solid transparent;
    border-radius: 40px
}

.gg-close::after,
.gg-close::before {
    content: "";
    display: block;
    box-sizing: border-box;
    position: absolute;
    width: 16px;
    height: 2px;
    background: currentColor;
    transform: rotate(45deg);
    border-radius: 5px;
    top: 7px;
    left: 1px
}

.gg-close::after {
    transform: rotate(-45deg)
}

.gg-spinner {
    transform: scale(var(--ggs, 1))
}

.gg-spinner,
.gg-spinner::after,
.gg-spinner::before {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    margin: 5px;
    left: -2px
}

.gg-spinner::after,
.gg-spinner::before {
    content: "";
    position: absolute;
    border-radius: 100px
}

.gg-spinner::before {
    animation: spinner 1s cubic-bezier(.6, 0, .4, 1) infinite;
    border: 3px solid var(--dhl-color-yellow);
    border-top-color: var(--dhl-color-red)
}

.gg-spinner::after {
    border: 3px solid;
    opacity: .2
}

@keyframes spinner {
    0% {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(359deg)
    }
}

.gg-danger {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    transform: scale(var(--ggs, 1));
    width: 20px;
    height: 20px;
    border: 2px solid;
    border-radius: 40px;
    margin: 0px 4px;
}

.gg-danger::after,
.gg-danger::before {
    content: "";
    display: block;
    box-sizing: border-box;
    position: absolute;
    border-radius: 3px;
    width: 2px;
    background: currentColor;
    left: 7px
}

.gg-danger::after {
    top: 2px;
    height: 8px
}

.gg-danger::before {
    height: 2px;
    bottom: 2px
}

.gg-external {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    transform: scale(var(--ggs, 1));
    width: 8px;
    height: 8px;
    box-shadow: -2px 2px 0 0,
    -4px -4px 0 -2px,
    4px 4px 0 -2px;
    margin-left: 4px;
    margin-right: 4px;
    margin-top: 1px
}

.gg-external::after,
.gg-external::before {
    content: "";
    display: block;
    box-sizing: border-box;
    position: absolute;
    right: -4px
}

.gg-external::before {
    background: currentColor;
    transform: rotate(-45deg);
    width: 10px;
    height: 2px;
    top: 0px
}

.gg-external::after {
    width: 6px;
    height: 6px;
    border-right: 2px solid;
    border-top: 2px solid;
    top: -4px
}