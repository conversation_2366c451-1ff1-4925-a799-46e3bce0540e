.results{
    border-collapse: collapse;
    width: 100%;
    background-color: white;
    margin: 10px 0;
}

.results td, .results th {
    border: var(--dhl-heading-border);
    padding: 3px;
}

.results td{
	color: #666666;
}

tr.results:hover {
    background-color: var(--dhl-color-lightest-yellow);
}

.results th {
    color: var(--dhl-color-dark-gray);
    background-color: var(--dhl-color-lightest-gray);
    text-align: left;
}

.error-table {
  border-collapse: collapse;
  width: 100%;
  background-color: white;
  font-size: smaller;
    margin: 10px 0;
}

.error-table td, .error-table th {
    border: var(--dhl-heading-border);
    padding: 8px;
}

.error-table tr:hover {
  background-color: #ddd;
}