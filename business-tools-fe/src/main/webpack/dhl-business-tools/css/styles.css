:root {
    --main-border: 1px solid #F1F1F1;
}

.alert {
    padding: 10px;
    color: var(--dhl-color-dark-gray);
    font-weight: bolder;
    margin-bottom: 10px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    border-radius: 4px;
}

/* The close button */
.closebtn {
    margin-left: 15px;
    color: white;
    font-weight: bold;
    float: right;
    font-size: 22px;
    line-height: 20px;
    cursor: pointer;
    transition: 0.3s;
}

.width-full {
    width: 100%;
}

.width-half {
    width: 50%;
}


/* When moving the mouse over the close button */
.closebtn:hover {
    color: #ffcc00;
}

.my-container {
    padding: 0.01em 16px;
    /* border-bottom: 1px solid black; */
}

.flex-container-generic {
    display: flex;
    flex-wrap: wrap;
}

.flex-container {
    margin: 5px auto 5px auto;
    line-height: 1.65;
    padding: 20px 50px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 90%;
    justify-content: space-between;
}

.flex-container-narrow {
    margin: 5px auto 5px auto;
    line-height: 1.65;
    padding: 20px 50px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 50%;
    justify-content: space-between;
}

.flex-container-column {
    margin: 15px auto 5px auto;
    line-height: 1.65;
    padding: 20px 50px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    flex-wrap: nowrap;
}

.flex-child-generic {
    flex: 1;
}

.flex-child-generic2 {
    flex: 2;
}

.flex-child-generic3 {
    flex: 3;
}

.flex-child-generic4 {
    flex: 4;
}

.flex-inner-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 100%;
    justify-content: space-between;
}

.flex-child-tiny {
    box-sizing: border-box;
    flex-grow: 1;
    padding: 0.5em 0.5em;
    overflow: hidden;
    list-style: none;
}

.flex-child-large {
    flex: 10;
}

.flex-child-form {
    box-sizing: border-box;
    padding: 15px;
    width: 65%;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
}

.flex-child-info {
    box-sizing: border-box;
    padding: 10px;
    width: 30%;
}

.flex-child-info-no-pad {
    box-sizing: border-box;
    width: 50%;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
}

.form-group label {
    display: block;
    font-size: 14px;
}

.form-group .input-control,
.form-group .file-upload-input,
.form-group .input-dropdown {
    flex: 1 1 auto;
    display: block;
    margin: 2px;
    font-family: 'Delivery', monospace;
    font-size: 14px;
}

.form-group ::placeholder {
    color: lightgrey;
}

.form-group .input-dropdown {
    height: 38px;
    border-radius: 4px;
    border: solid 1px #c8c8c8;
}

.form-group .input-control {
    border-radius: 4px;
    border: solid 1px #c8c8c8;
    height: 2rem;
}

.form-group .file-upload-input {
    border: none;
    height: 2rem;
}

input[type=file]::file-selector-button {
    border: 2px solid #ccc;
    padding: .2em .4em;
    border-radius: .4em;
    background-color: #ccc;
    transition: 1s;
    height: 2rem;
}

input[type=file]::file-selector-button:hover {
    background-color: #c8c8c8;
    border: 2px solid #FFCC00;
}

.form-group input[type="checkbox"] {
    flex: none;
}

button {
    padding: 5px 15px;
    margin: 2px;
    min-width: 100px;
    text-decoration: none;
    font-size: 15px;
    cursor: pointer;
    transition-duration: 0.2s;
    border: 1px solid #D40511;
    height: 32px;
}

.button-green {
    color: white;
    background-color: #007F39FF;
    margin: 5px 2px;
}

.button-green:hover {
    color: #007F39FF;
    background-color: white;
}

.button-submit {
    color: white;
    background-color: #D40511;
    margin: 5px 2px;
}

.button-submit:hover {
    color: #D40511;
    background-color: white;
}

button:disabled {
    background-color: #F1F1F1;
    color: #666666;
}

button:disabled:hover {
    background-color: #F1F1F1;
    color: #666666;
    cursor: default;
}

.button-reset {
    color: white;
    background-color: #D40511;
}

.button-reset:hover {
    color: #D40511;
    background-color: white;
    cursor: pointer;
}

.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted black;
}

.tooltip .tooltiptext {
    border: 1px solid black;
    visibility: hidden;
    width: 240px;
    background-color: #FFCC00;
    color: #323232;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 150%;
    left: 50%;
    margin-left: -60px;
}

.tooltip .tooltiptext::after {
    content: " ";
    position: absolute;
    top: 100%;
    /* At the bottom of the tooltip */
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
}

.hidden {
    visibility: hidden;
}

.invisible {
    display: none;
}

.collapsed {
    visibility: collapse;
}

.visible {
    display: block;
}

.warning {
    background-color: #FCE07F;
}

.ajaxResponse {
    padding: 5px;
    color: #323232;
}

div.livecopy-checkbox {
    border: 1px solid var(--main-text-color);
    padding: 5px;
    border-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
}

div.livecopy-checkbox:last-child {
    border: 1px solid var(--main-text-color);
}

div.livecopy-form-styling {
    background-color: white;
    color: #323232;
    padding: 5px;
}

div.livecopy-form-heading {
    padding: 5px;
}

i.livecopy-form-heading {
    text-align: right;
    padding: 5px;
}

div.well-large {
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    width: 80%;
    border: var(--main-border);
    padding: 7px;
}

div.well-x-large {
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    width: 90%;
    border: var(--main-border);
    padding: 7px;
}

div.well-xx-large {
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    border: var(--main-border);
    padding: 7px;
}

div.well-medium {
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    width: 70%;
    border: var(--main-border);
    padding: 7px;
}

div.well-small {
    margin-top: 10px;
    margin-left: auto;
    margin-right: auto;
    width: 50%;
    border: var(--main-border);
    padding: 7px;
    border-radius: 4px;
}

div.shadowed {
    box-shadow: 5px 5px 5px 0 var(--main-border);
}

.checkbox-control {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1.1;
}

tbody {
    font-size: smaller;
}

.flex-container.flex-table,
.flex-container #uploadRedirects {
    justify-content: center;
}

.confirm-continue {
    margin-top: 0.7rem;
}

.reference-lookup-result {
    justify-content: center;
}

.reference-form {
    width: 33%;
}

.table-head-65pc {
    width: 65%;
}

.table-row-font-14px {
    font-size: 14px;
}

#overlay {
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.7);
    overflow-x: hidden;
    transition: 0.1s;
}

#overlay-content {
    position: relative;
    top: 25%;
    width: 100%;
    text-align: center;
    margin-top: 30px;
    color: var(--dhl-color-lightest-gray);
}

.redirect-status-empty,
.i18n-dictionary-empty,
#status-table,
#uploadResult,
#livecopy-empty-data {
    display: none;
}

#status-table {
    width: auto;
}

#status-table-body {
    font-size: smaller;
}

.form-checkbox {
    display: inline-flex;
    cursor: pointer;
    position: relative;
}

.form-checkbox span {
    color: #666666;
    padding: 0.5rem 0.25rem;
}

.confirm-checkbox-span {
    line-height: 1.5rem;
}

.mass-update-checkbox-span {
    line-height: 0.7rem;
}

.select-checkbox-span {
    line-height: 0.5rem;
    font-size: 1rem;
}

.form-checkbox input {
    height: 20px;
    width: 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid #323232;
    border-radius: 4px;
    outline: none;
    transition-duration: 0.3s;
    background-color: #fff;
    cursor: pointer;
}

input[type=checkbox].input-control {
    height: 20px;
    width: 20px;
    border: 1px solid #323232;
    border-radius: 4px;
    outline: none;
    transition-duration: 0.3s;
    background-color: #fff;
    cursor: pointer;
}

.form-checkbox input:checked {
    border: 1px solid #323232;
}

.form-checkbox input:checked+span::before {
    content: '\2713';
    display: block;
    text-align: center;
    color: #d40511;
    position: absolute;
    left: 0.5rem;
    top: 0.5rem;
}

.form-checkbox input:active {
    border: 2px solid #34495E;
}

.activation-error {
    color: #d40511;
}

.activation-result {
    font-weight: bold;
}

.locales-select {
    width: 200px;
}

.locales-select select {
    border-radius: 0.25em;
    line-height: 1.1;
}

select {
    appearance: none;
    background-color: transparent;
    padding: 0 1em 0 1em;
    margin: 2px;
    width: 40%;
    height: 32px;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;

    background-size: 2rem .8rem;
    border: .1rem solid #949494;
    cursor: pointer;
    color: var(--main-text-color);
}

.i18n-locale-modal,
.i18n-activation-locale-modal {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    background: #fff;
    border: 1px solid #000;
}

.i18n-locale-modal {
    width: 96%;
}

.i18n-activation-locale-modal {
    width: auto;
    height: auto;
}

.i18n-loading-modal {
    width: auto;
    height: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin: 2.5% 2%;
    position: absolute;
    background: #fff;
}

.i18n-locales-modal-wrapper,
.i18n-modal-wrapper,
.i18n-activation-modal-wrapper,
.i18n-loading-modal-wrapper {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #949494e8;
    z-index: 11;
}

.i18n-locales-modal-wrapper,
.i18n-modal-wrapper {
    z-index: 11;
}

.i18n-activation-modal-wrapper {
    z-index: 12;
}

.i18n-locale-modal-content {
    padding: 10px;
}

#i18n-locale-list-close,
#i18n-activation-buttons,
#i18n-activation-locale-modal-close {
    float: right;
}

.flex-child-modal {
    width: 50%;
    box-sizing: content-box;
}

.i18n-modal-container {
    max-height: 50rem;
    overflow: auto;
    line-height: 1.65;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 100%;
    justify-content: space-between;
}

.i18n-strings-checkboxes {
    margin: 5px;
}

.i18n-strings-checkboxes[disabled] {
    pointer-events: none;
    opacity: 0.5;
}

span.locale-link {
    text-decoration: underline;
    color: #d40511;
    font-size: small;
    line-height: 0.7rem;
}

.btn-activate {
    margin-bottom: 10px;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.tdc-tenant-dropdown:hover {
    color: var(--dhl-color-dark-gray);
}

.right-align {
    float: right;
}
