/* this style class is all about centering the content on the screen horizontally and vertically - we use flexbox for this */
main{
  background-color: white;
  height: 90%;
  width: 80%;
  margin: 0 auto;
  padding: 20px;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

main div{
  width: 75%;
  padding: 20px;
  overflow: auto;
}
/* end of style class centering content horizontally and vertically on the screen */

a{
  color: #d40511;
}

a:hover{
  color: #323232;
}
