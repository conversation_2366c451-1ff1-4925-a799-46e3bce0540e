/**
 * @jest-environment jsdom
 */
//require('jest-fetch-mock').enableMocks()
import '@testing-library/jest-dom';
import {exportObjects as general} from "../main/webpack/dhl-business-tools/js/general";
import fetchMock from "jest-fetch-mock";
//import {startMirage} from "./miragejs/server";
//import {server} from "./msw/mws-server";

fetchMock.enableMocks();

beforeEach(() => {
  fetch.resetMocks();
  // server.listen();
})
afterEach(() => {
  // server.resetHandlers();
  // server.close();
  return clearDom();
})

afterAll(() => {
  // server.close();
})

const MOCK_RESPONSE = "{'test': true}";

function clearDom() {
  document.body.innerHTML = "";
}

test('sanitizeString | tests string sanitization', () => {
  expect(general.sanitizeString("bla")).toBe("bla");
})

test('sanitizeString | testing string sanitization 2', () => {
  expect(general.sanitizeString('divide & conquer > swords to plowshares')).toBe('divide &amp;'
      + ' conquer &gt; swords to plowshares')
})

let tableHeaderColumns = ["col1", "col2"];
test('buildTableHeader | Testing creation of table header', () => {
  expect(general.buildTableHeader(tableHeaderColumns)).toBe('<thead><tr><th>col1</th><th>col2</th></tr></thead>')
})

describe('Show or Hide HTML elements', () => {
  it.each`
    presentClass | classWeWantSet | functionParam 
    ${'visible'} | ${'invisible'} | ${'hide'} 
    ${'invisible'} | ${'visible'} | ${'show'}
  `
  ("showHideElement | Testing js functionality to '$functionParam' and setting css class to"
      + " contain '$classWeWantSet'",({presentClass, classWeWantSet, functionParam}) => {
    document.body.innerHTML = `
    <div id="dummy-id" class="${presentClass}">dummy-div-contents</div>
  `
    general.showHideElement("dummy-id", `${classWeWantSet}`, `${functionParam}`)
    let element = document.getElementById("dummy-id")

    if(classWeWantSet === 'invisible') {
      !expect(element).toBeVisible();
    } else {
      expect(element).toBeVisible();
    }
  });
});

describe('Show or Hide HTML elements FAILURE', () => {
  it.each`
    elementId | functionParam 
    ${'cat'} | ${'pur'} 
    ${'dummy-id'} | ${'badParam'}
  `
  ("showHideElement | Testing bad params provided to showHideElement fn",({elementId, functionParam}) => {
    document.body.innerHTML = `
    <div id="dummy-id" class="zzzz">dummy-div-contents</div>
  `
    general.showHideElement(`${elementId}`, "zzzz", `${functionParam}`)
    let element = document.getElementById(`${elementId}`)
    if(elementId === 'dummy-id'){
      expect(element.classList).toContain("zzzz");
    } else {
      expect(element).toBeNull()
    }
  });
});

test('getNumOfChecked | Counting checkboxes', () => {
  document.body.innerHTML = `
    <button id="test-button">button text</button>
    <button id="another-button">button text</button>
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" checked>  
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz" checked>
    <input name="different-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz">
  `
  general.getNumOfChecked("locale-checkbox","test-button", "button text");
  general.getNumOfChecked("different-checkbox", "another-button", "button-text");
  let button = document.getElementById("test-button");
  let disabledButton = document.getElementById("another-button")
  expect(button.disabled).toBeFalsy();
  expect(disabledButton.disabled).toBeTruthy()
})

test('getCheckboxes | Getting array of checkboxes', () => {
  document.body.innerHTML = `
    <button id="test-button">button text</button>
    <button id="another-button">button text</button>
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" checked>  
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz" checked>
    <input name="different-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz">
  `

  const arrayOfCheckboxes = general.getCheckboxes("locale-checkbox");
  expect(arrayOfCheckboxes.length).toBe( 2);
})

describe('Updating button text', () => {
  it.each`
    elementId | newText | count
    ${'dummy-id'} | ${'new button text'} | ${0}
    ${'dummy-id'} | ${'new button text'} | ${1}
    ${'bad-id'} | ${'unchanged button text'} | ${2}
  `
  (`updateButtonText | Testing Button change id: '$elementId', new button text: '$newText', count: '$count'`,({elementId, newText, count}) => {
    document.body.innerHTML = `
    <button id="dummy-id">${newText}</button>
  `
    general.updateButtonText(newText, elementId, count);
    let element = document.getElementById(`${elementId}`)

    if(elementId === 'bad-id'){
      expect(element).toBeNull();
    }

    if(count === 0){
      expect(element.disabled).toBeTruthy();
    }

    if(count === 1){
      expect(element.disabled).toBeFalsy();
    }
  });
});

describe('Testing adding empty data row to a table', () => {
  test.each([
      [0],
      [2]
  ])
  ( 'addEmptyDataRow | Testing adding empty data row to a table with span (%i)', (tableColumnsNo) => {
      document.body.innerHTML = `
      <table>
          <thead>
              <tr>
              <th>col1</th>
              <th>col2</th>
              </tr>
          </thead>
          <tbody id="table-body">
          </tbody>
      </table>
        `
      const tableBody = document.getElementById("table-body");
      general.addEmptyDataRow("text to be in the cell", tableBody, tableColumnsNo);
      const tableRows = tableBody.getElementsByTagName("td");

      const tableCell = tableRows[0];
      if (tableColumnsNo !== 0) {
        expect(tableRows.length).toBe(1);
        expect(tableCell.innerText).toBe("text to be in the cell");
        expect(tableCell.classList).toContain("text-center");
      }

      if (tableColumnsNo === 0){
        expect(tableCell).toBeUndefined()
      }
    }
  )
});

describe('Testing if provided string exists in the provided array', () => {
  test.each([
    ["text1", ["text1", "another string"], true],
    ["text2", ["another string"], false],
    ["text3", [], false],
    ["text4", ["text4", "another string", "text4"], true]
  ])
  ( 'doesColumnContainDateValue | Testing if provided text "%s" exists in the array', (string, array, expected) => {
    expect(general.doesColumnContainDateValue(string, array)).toBe(expected);
  })
});

test('addTableCell | Adding table cell to existing table row', () => {
  document.body.innerHTML = `
    <table>
        <tr id="table-row"></tr>
    </table>
  `
  const tableRow = document.getElementById("table-row");
  general.addTableCell(tableRow, "text to be in the cell");
  expect(tableRow.getElementsByTagName("td")[0].innerText).toBe("text to be in the cell");
});

test('addTableBodyWithId | Add table body to existing table', () => {
  document.body.innerHTML = `
  <table id="bt-table"></table>
  `

  const table = document.getElementById("bt-table");
  general.addTableBodyWithId("bt-table-body", table);
  expect(table.getElementsByTagName("tbody")[0].id).toBe("bt-table-body");
})

describe('Add table header to existing table', () => {
  const columnsMap = new Map();
  columnsMap.set("column-1", "col1");
  columnsMap.set("column-2", "col2");
  columnsMap.set("column-3", "col3");
  const columnsArray = ["column-1", "column-2", "column-3"];

  test.each ([
      [columnsMap, "map"],
      [columnsArray, "array"]
  ])('addTableHeader | Add table header to existing table from "%o", type "%s"', (columns) => {
      document.body.innerHTML = `
        <table id="bt-table"></table>
        `
      const table = document.getElementById("bt-table");
      general.addTableHeader(columns, table);
      const tableHeader = table.getElementsByTagName("thead")[0];
      expect(tableHeader.getElementsByTagName("th").length).toBe(3);
      expect(tableHeader.getElementsByTagName("th")[0].innerText).toBe("column-1");
  });
});



test('uncheckedCheckboxes | Uncheck all checkboxes on a page', () => {
  document.body.innerHTML = `
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" checked>  
    <input name="locale-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz" checked>
    <input name="different-checkbox" type="checkbox" value="/apps/dhl/i18n/dhl-global/cz">
  `
  general.uncheckedCheckboxes();
  const checkboxes = document.getElementsByName("locale-checkbox");
  for (let checkbox of checkboxes) {
    expect(checkbox.checked).toBeFalsy();
  }
});

describe('Check or uncheck checkboxes', () => {
  test.each([
    ['checked'],
    ['unchecked']
  ])
  ('toggleCheckboxes | Toggle checkboxes | source checkbox: %s', (checked) => {
    document.body.innerHTML = `
    <input type="checkbox" name="source-checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" id="source-checkbox" ${checked}>
    <input type="checkbox" name="locale-checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" checked>
    <input type="checkbox" name="locale-checkbox" value="/apps/dhl/i18n/dhl-global/cz_cs" > 
  `
    const sourceCheckboxElement = document.getElementById("source-checkbox");
    general.toggleCheckboxes(sourceCheckboxElement, 'locale-checkbox');
    const checkboxes = document.getElementsByName("locale-checkbox");
    for (let checkbox of checkboxes) {
      if (checked === 'checked') {
        expect(checkbox.checked).toBeTruthy();
      } else {
        expect(checkbox.checked).toBeFalsy()
      }
    }
  });
});

describe('Enable or disable button', () => {
  test.each([
    ['checked'],
    ['unchecked']
  ])
  ('toggleButton | Toggle button | inputs: %s', (checked) => {
    document.body.innerHTML = `
    <input type="checkbox" name="locale-checkbox" value="/apps/dhl/i18n/dhl-global/bs_ba" ${checked}>
    <input type="checkbox" name="locale-checkbox" value="/apps/dhl/i18n/dhl-global/cz_cs" > 
    <button id="test-button">button text</button>
  `
    general.toggleButton('test-button', 'locale-checkbox');
    if(checked === 'checked') {
      expect(document.getElementById('test-button').disabled).toBeFalsy();
    } else {
      expect(document.getElementById('test-button').disabled).toBeTruthy();
    }
  });
})

describe('Testing fetch variations', () => {
  beforeEach(() => {
    fetch.resetMocks();
  })

  const url = 'https://dummy-url.com';
  const method = 'POST';
  const formData = new FormData();
  formData.append('field1', 'value1');
  const overlay = true;


  test('NO csrf token', async () => {
    fetch.mockResponseOnce(JSON.stringify({data: '12345'}), {status: 200});
    await general.submitFormFetch(url, method, formData, null, overlay);

    expect(fetch.mock.calls[0][1].headers).toBeUndefined();
    expect(fetch.mock.calls.length).toEqual(1);
    expect(fetch.mock.calls[0][0]).toEqual('https://dummy-url.com');
    })

  test('NO form data', async () => {
    fetch.mockResponseOnce(JSON.stringify({data: '12345'}), {status: 200});
    await general.submitFormFetch(url, method, null, '12345', overlay);

    expect(fetch.mock.calls[0][1].formData).toBeUndefined();
    expect(fetch.mock.calls.length).toEqual(1);
    expect(fetch.mock.calls[0][0]).toEqual('https://dummy-url.com');
  })

  test('Submit form fetch negative', async () => {
    const message = 'Error communicating with AEM';
    const error = new Error(message);
    fetch.mockReject(error);

    await expect(general.submitFormFetch(url, method, formData, overlay)).rejects.toEqual(error);
  })

  test('Submit form fetch positive', async () => {
    fetch.mockResponseOnce(JSON.stringify({data: '12345'}), {status: 200});

    await general.submitFormFetch(url, method, formData, overlay).then(response => {
      expect(response.data).toBe('12345');
    })
  })
})



