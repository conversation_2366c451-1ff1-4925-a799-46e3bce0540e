import {createServer} from "miragejs";

export function startMirage  () {
    return createServer({
        routes() {
            this.post('/apps/dhl-business-tools/servlet-handler/akamai.validate-input.html', (schema, request) => {
                let requestHeaders = request.requestHeaders;
                let requestBody = request.requestBody;
                console.log(requestBody)
                return JSON.stringify(requestHeaders)
            });
        },
    })
}
