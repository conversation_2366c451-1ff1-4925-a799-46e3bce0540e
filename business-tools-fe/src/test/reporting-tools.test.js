/**
 * @jest-environment jsdom
 */
//require('jest-fetch-mock').enableMocks()
import '@testing-library/jest-dom';
import { exportedFunctions as reporting } from "../main/webpack/dhl-business-tools/js/reporting-tools";
import fetchMock from "jest-fetch-mock";

fetchMock.enableMocks();

beforeEach(() => {
  fetch.resetMocks();
})
afterEach(() => {
  return clearDom();
})

afterAll(() => {
})

function clearDom() {
  document.body.innerHTML = "";
}


test('getTenantsDropdown | references-tenant-dropdown', async () => {

  document.body.innerHTML = `
  <div class="form-group">
      <label class="dhl-color-text-dark-gray" for="references-tenant-dropdown">Tenant</label>
      <select class="input-dropdown" id="references-tenant-dropdown" name="tenant" required="">
          <option value="">Select a Tenant</option>
      </select>
  </div>
  `
  const tenants = [
    { "tenant": "dhl", "countries": [{ "countryName": "Afghanistan", "countryCode": "af", "path": "/content/dhl/af" }] },
    { "tenant": "deutschepostdhl", "countries": [{ "countryName": "EN", "countryCode": "en", "path": "/content/deutschepostdhl/en" }] }
  ]

  fetch.mockResponseOnce(JSON.stringify({ tenants }), { status: 200 });
  await reporting.getTenantsDropdown("references-tenant-dropdown", "unreferenced-pages");

  expect(fetch.mock.calls.length).toEqual(1);
  expect(document.getElementsByTagName("option").length).toEqual(3);
})


