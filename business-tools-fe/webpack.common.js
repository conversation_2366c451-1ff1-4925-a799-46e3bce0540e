'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
require('tsconfig-paths-webpack-plugin');
const {CleanWebpackPlugin} = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const SOURCE_ROOT = __dirname + '/src/main/webpack';

const {
  BannerPlugin
} = require('webpack');

const packageJson = require('./package.json');
const bannerPrefix = `project: ${packageJson.name} v${packageJson.version} | compilation hash: [fullhash] | timestamp: `;

// eslint-disable-next-line no-undef
module.exports = {
  infrastructureLogging: {
    level: 'verbose',
  },
  entry: {
    site: SOURCE_ROOT + '/dhl-business-tools/main.js'
  },
  output: {
    filename: 'clientlib-site/business-tools.js',
    path: path.resolve(__dirname, 'dist'),
    clean: true,
  },
  optimization: {
    splitChunks: {
      chunks: 'all'
    }
  },
  module: {
    rules: [
      {
        test: /\.(sa|sc|c)ss$/i,
        use: [
          MiniCssExtractPlugin.loader,
          "css-loader",
        ],
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'resources/[name][ext]'
        }
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
      },
    ]
  },
  plugins: [
    new CleanWebpackPlugin(),
    new ESLintPlugin({
      extensions: ['js', 'ts', 'tsx']
    }),

    new MiniCssExtractPlugin({
      filename: 'clientlib-site/business-tools.css',
    }),

    new BannerPlugin({
      banner: `${bannerPrefix}${new Date()}`
    }),
  ],
  stats: {
    assetsSort: 'chunks',
    builtAt: true,
    children: false,
    chunkGroups: true,
    chunkOrigins: true,
    colors: true,
    errors: true,
    errorDetails: true,
    env: true,
    modules: false,
    performance: true,
    providedExports: false,
    source: false,
    warnings: true,
    logging: true,
  }
};

