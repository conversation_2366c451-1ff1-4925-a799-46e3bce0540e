name: Create PR

on:
  push:
    branches:
      - 'release/**'

jobs:
  createPr:
    name: Create PR
    runs-on: [akamai]
    env:
      GH_HOST: "git.dhl.com"
      GH_REPO: "NDCW-10025/log-dev-dhl-dex"
      GH_ENTERPRISE_TOKEN: ${{ github.token }}
      ACTIONS_RUNNER_DEBUG: true
      ACTIONS_STEP_DEBUG: true

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Github OAuth credentials
        id: auth_app_token
        uses: NDCW-10025-actions/auth-app-token@v2
        with:
          app_id: ${{ secrets.OAUTH_APP_ID }}
          private_key: ${{ secrets.OAUTH_APP_PRIVATE_KEY }}

      - name: Extract branch name
        id: branch_name
        run: |
          echo "branch_name=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV

      - name: Create PR into master
        run: |
          gh pr create -B master --title "Back-merge from ${{ env.branch_name }} to master" --body "Automatically created by Github action" || echo "PR already exists"

      - name: Create PR into develop
        env:
          GH_ENTERPRISE_TOKEN: ${{ steps.auth_app_token.outputs.token }}
        run: |
          gh pr create -B develop --title "Back-merge from ${{ env.branch_name }} to develop" --body "Automatically created by Github action" &> to_devel.txt || echo "PR already exists"
          PRID=`cat to_devel.txt | grep "https://git.dhl.com" | sed -n 's#.*/pull/\([0-9]*\)#\1#p'`
          if [ -n "$PRID" ]; then
            sleep 2
            echo "Merging PR #$PRID"
            gh pr merge $PRID --merge --admin
          fi
          