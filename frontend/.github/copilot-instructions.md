{"github.copilot.chat.testGeneration.instructions": [{"text": "Use Jest for testing JavaScript code."}, {"text": "Use jest-environment-jsdom for DOM-related testing."}, {"text": "Consider already existing istanbul ignore comments."}, {"text": "Avoid testing console statements or code which is meant for test environments only."}, {"text": "Prefer the it() call over the test() call."}, {"text": "Use latest ECMAScript syntax but avoid features which might still be experimental."}, {"text": "Avoid using syntax which is not meant for the browser, e.g. prefer ECMAScript import or dynamic import() over Node.js require()."}, {"text": "Prefer async/await over promises."}, {"text": "Use JSDoc-compliant comments with brief description (without leading @description) and optional @param, @returns and @throws whenever possible."}, {"text": "Generally avoid inline comments."}, {"text": "Add a brief inline comment above each describe() and it() call."}, {"text": "Add a brief inline comment when a specific line might be hard to understand."}, {"text": "Use tabs instead of spaces and limit the length of comments to 80 chars per line."}, {"text": "Add a blank line before every inline comment."}, {"text": "Ensure the code coverage for branches, functions, lines and statements is 100%."}, {"text": "Consider that fakeTimers are globally enabled in the main jest.config.js."}, {"text": "Consider that resetModules is globally enabled in the main jest.config.js."}, {"text": "Consider that clearMocks is globally enabled in the main jest.config.js."}, {"text": "Consider that resetMocks is globally enabled in the main jest.config.js."}, {"text": "Consider that restoreMocks is globally enabled in the main jest.config.js."}]}