const Promise = require('bluebird');
const path = require('path');
const fs = require('fs-extra');
const chalk = require('chalk'); // we should stick with v4.x.x for now since v5 introduces ESM syntax
const chalkError = chalk.bold.red;
const del = require('del'); // async by default; we should stick with v6.x.x for now since v7 introduces ESM syntax

/**
 * Cleans the specified files or file patterns using del.
 *
 * @param {string|string[]} filePaths - The files or file patterns to be cleaned.
 * @param {string|string[]} [exclusionPaths] - The files or file patterns to be excluded from cleaning.
 * @returns {Promise<void>} A promise that resolves when the cleaning is done.
 */
const _clean = async (filePaths, exclusionPaths = []) => {
	const filePathArray = typeof filePaths === 'string' ? [
		filePaths
	] : filePaths;
	const exclusionPathArray = typeof exclusionPaths === 'string' ? [
		exclusionPaths
	] : exclusionPaths;

	const finalFilePaths = filePathArray.concat(exclusionPathArray.map((exclusionPath) => `!${exclusionPath}`));

	try {
		await del(finalFilePaths, {
			force: true, // allow deleting outside the working directory
			dot: true // allow deleting files starting with a dot, also without explicite dot in the pattern
		});
		console.log('[utils/clean] Done');
	}
	catch (error) {
		console.error(chalkError(`[utils/clean] Error: ${error}`));
		throw error;
	}
};

/**
 * Replaces occurrences of a regular expression with new content in a file.
 *
 * @param {RegExp} regex - The regular expression to match.
 * @param {String} newContent - The new content to replace the matches with.
 * @param {String} filePath - The path to the file.
 * @returns {Promise<boolean>} A promise that resolves to true if the replacements were successful, or rejects with an error.
 */
const _replaceInFile = (regex, newContent, filePath) => new Promise((resolve, reject) => {
	let file;
	let output = '';

	try {
		file = fs.createReadStream(filePath, 'utf8');
	}
	catch (error) {
		console.error(chalkError(`[utils/replace-in-file] Error on readStream: ${error}`));
	}

	file.on('data', (chunk) => {
		output += chunk.toString().replace(regex, newContent);
	});
	file.on('error', (error) => {
		console.error(chalkError(`[utils/replace-in-file] Error: ${error}`));

		return reject(error);
	});

	file.on('end', () => {
		fs.writeFile(filePath, output, (error) => {
			if (error) {
				console.error(chalkError(`[utils/replace-in-file] Error: ${error}`));

				return reject(error);
			}

			console.log(`[utils/replace-in-file] Replaced matches for "${regex}" in ${filePath}`);
			resolve(true);
		});
	});
});

/**
 * Adds or overwrites a file with the given content at the specified file path.
 *
 * @param {String} content - The content to be written to the file.
 * @param {String} filePath - The path of the file to be added or overwritten.
 * @returns {Promise<boolean>} - A promise that resolves to true if the file was successfully added or overwritten, otherwise rejects with an error.
 */
const _addFile = async (content, filePath) => {
	try {
		const dir = path.dirname(filePath);

		await fs.ensureDir(dir);
		await fs.writeFile(filePath, content);

		console.log(`[utils/add-file] Added or overwritten ${filePath}`);

		return true;
	}
	catch (error) {
		console.error(chalkError(`[utils/add-file] Error: ${error}`));
		throw error;
	}
};

/**
 * Adds the specified content to the given file path.
 *
 * @param {String} content - The content to be added to the file.
 * @param {String} filePath - The path of the file to which the content will be added.
 * @returns {Promise<boolean>} A promise that resolves to true if the content is successfully added, or rejects with an error.
 */
const _addInFile = (content, filePath) => new Promise((resolve, reject) => {
	let file;
	let output = '';

	try {
		file = fs.createReadStream(filePath, 'utf8');
	}
	catch (error) {
		console.error(chalkError(`[utils/add-in-file] Error on readStream: ${error}`));
	}

	file.on('data', (chunk) => {
		output += chunk;
		output += content;
	});
	file.on('error', (error) => {
		console.error(chalkError(`[utils/add-in-file] Error: ${error}`));

		return reject(error);
	});

	file.on('end', () => {
		if (output === '') {
			output = content;
		}

		fs.writeFile(filePath, output, (error) => {
			if (error) {
				console.error(chalkError(`[utils/add-in-file] Error: ${error}`));

				return reject(error);
			}

			console.log(`[utils/add-in-file] "${content}" in ${filePath}`);
			resolve(true);

		});
	});
});

/**
 * Deletes lines matching a regular expression in a file.
 *
 * @param {RegExp} regexp - The regular expression to match lines.
 * @param {String} filePath - The path to the file.
 * @returns {Promise<boolean>} A promise that resolves to true if the lines were successfully deleted, or rejects with an error.
 */
const _deleteInFile = (regexp, filePath) => new Promise((resolve, reject) => {
	let file;
	let output = '';

	try {
		file = fs.createReadStream(filePath, 'utf8');
	}
	catch (error) {
		console.error(chalkError(`[utils/delete-in-file] Error on readStream: ${error}`));
	}

	file.on('data', (chunk) => {
		const lines = chunk.split('\n');
		const filteredLines = lines.filter((line) => line && !line.match(regexp));

		output = `${filteredLines.join('\n')}\n`;
	});
	file.on('error', (error) => {
		console.error(chalkError(`[utils/delete-in-file] Error: ${error}`));

		return reject(error);
	});

	file.on('end', () => {
		fs.writeFile(filePath, output, (error) => {
			if (error) {
				console.error(chalkError(`[utils/delete-in-file] Error: ${error}`));

				return reject(error);
			}

			console.log(`[utils/delete-in-file] "${regexp}" in ${filePath}`);
			resolve(true);

		});
	});
});

/**
 * Renames a file from the old path to the new path.
 *
 * @param {String} oldPath - The path of the file to be renamed.
 * @param {String} newPath - The new path for the renamed file.
 * @returns {Promise<boolean>} - A promise that resolves to true if the file is successfully renamed, or rejects with an error.
 */
const _renameFile = async (oldPath, newPath) => {
	try {
		await fs.rename(oldPath, newPath);
		console.log(`[utils/rename-file] Renamed ${oldPath} to ${newPath}`);

		return true;
	}
	catch (error) {
		console.error(chalkError(`[utils/rename-file] Error: ${error}`));
		throw error;
	}
};

module.exports = {
	clean: _clean,
	replaceInFile: _replaceInFile,
	addFile: _addFile,
	addInFile: _addInFile,
	deleteInFile: _deleteInFile,
	renameFile: _renameFile
};
