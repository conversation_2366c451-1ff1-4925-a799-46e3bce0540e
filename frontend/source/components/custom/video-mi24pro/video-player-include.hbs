{{!--
  A simplified version of the video.hbs partial that only renders the player container,
  with automatic ID generation for use in gallery components.
--}}

{{!-- Generate a unique ID if one is not provided --}}
{{set-value "videoPlayerId" (random-id)}}

<div
  id="{{id}}{{#unless id}}{{get-value "videoPlayerId"}}{{/unless}}"
  dir="ltr"
  class="c-video--player js--video-player"
  data-video-id="{{videoID}}"
  data-player-id="{{playerID}}"
  {{~#if autoplay}} data-autoplay="{{autoplay}}"{{~/if}}
  {{#if analyticsData}}data-analytics="{{json-stringify analyticsData}}"{{/if}}
  {{#if dataAdvancedInteractionsType}}data-advanced-interactions-type="{{dataAdvancedInteractionsType}}"{{else}}data-advanced-interactions-type="dhl_utf_contentInteraction"{{/if}}
  role="figure"
  aria-label="{{#if headline}}{{headline}}{{else}}Video player{{/if}}"
  aria-busy="true"
  tabindex="0"
>
  <div class="c-video--error js--video-error"
    role="alert"
    aria-hidden="true"
    aria-live="assertive">
    <span class="c-video--error-text">{{#if a11y.errorMessage}}{{a11y.errorMessage}}{{else}}An error occurred while loading the video.{{/if}}</span>
  </div>

  <div class="c-video--loading js--video-loading" aria-hidden="true">
    <div class="c-video--loading-spinner"></div>
  </div>
</div>
