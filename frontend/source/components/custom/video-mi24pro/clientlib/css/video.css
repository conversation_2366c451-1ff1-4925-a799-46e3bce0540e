/* ==========================================================================
   Video Component Styles - Mobile First Approach
   ========================================================================== */

/* Base container styles
   ========================================================================== */
.c-video {
	position: relative;
}

/* Base styles for video container */
.c-video--container {
	position: relative;
	width: 100%;
	aspect-ratio: 16 / 9;
	background-color: var(--color-background-dark);
	contain: layout style paint;
	content-visibility: auto;
	contain-intrinsic-size: 0 56.25vw;
}

/* Component size variations
   ========================================================================== */
.component-small .c-video--player {
	max-width: 96rem;
}

.component-wide .c-video--player {
	max-width: 120rem;
}

.c-video:not(.component-small):not(.component-wide) .c-video--player {
	max-width: none;
}

/* Player container
   ========================================================================== */
.c-video--player {
	position: relative;
	width: 100%;
	aspect-ratio: 16 / 9;
	border-radius: 0.5rem;
	overflow: hidden;
	margin: 0 auto;
	background-color: var(--color-black-400);
}

/* Focus styles for video player accessibility */
.c-video--player:focus {
	outline: 0.2rem solid var(--color-primary-500, #0066cc);
	outline-offset: 0.2rem;
}

.c-video--player:focus:not(:focus-visible) {
	outline: none;
}

/* Error states
   ========================================================================== */
.c-video--error {
	position: absolute;
	top: 0.5rem;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: baseline;
	justify-content: center;
	color: var(--color-white-500);
	opacity: 0;
	visibility: hidden;
	transition: opacity 300ms ease, visibility 300ms ease;
	pointer-events: none;
	z-index: 999;
}

.c-video--error.is-visible {
	opacity: 1;
	visibility: visible;
}

.c-video--error-text {
	text-align: center;
	font-size: var(--font-size-m);
	background-color: var(--color-red-700);
	border-radius: 0.5rem;
	padding: 0.5rem 1rem;
}

/* Loading states
   ========================================================================== */
.c-video--loading {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: var(--color-black-400);
	z-index: 10;
	opacity: 1;
	transition: opacity 300ms ease;
}

.c-video--loading[aria-hidden="true"] {
	display: none;
}

/* Loading spinner */
.c-video--loading-spinner {
	width: 5rem;
	height: 5rem;
	border: 0.4rem solid var(--color-white-500);
	border-top-color: transparent;
	border-radius: 50%;
	animation: spin 1000ms linear infinite;
	will-change: transform;
	backface-visibility: hidden;
	transform: translateZ(0);
	z-index: 11;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

/* Typography - Base styles (mobile first)
   ========================================================================== */
.c-video--title {
	line-height: var(--size-line-height-5xl);
	margin-bottom: 0.7rem;
}

.c-video--headline {
	font-size: var(--size-font-2xl);
	margin: 0 0 1.4rem;
	text-align: center;
	line-height: var(--size-line-height-xl);
}

.c-video--transcript {
	font-size: var(--size-font-lg);
	margin-bottom: 3.5rem;
	text-align: center;
	line-height: var(--size-line-height-xl);
}

.c-video--transcript.small-transcript-text {
	font-size: var(--size-font-md);
	margin-bottom: 2.1rem;
}

.c-video--transcript-small {
	font-size: var(--font-size-s);
}

.c-video--description {
	color: var(--color-gray-600);
	text-align: left;
	opacity: 0.9;
	font-size: var(--size-font-sm);
	margin-top: 0.7rem;
	margin-bottom: 0;
	line-height: var(--size-line-height-xl);
}

/* Common text styles
   ========================================================================== */
.c-video--transcript p:last-child,
.c-video--description p:last-child {
	margin-bottom: 0;
}

/* Left aligned grid variant
   ========================================================================== */
.left-aligned-grid .c-video--headline {
	font-size: var(--size-font-4xl);
	text-align: left;
}

.left-aligned-grid .c-video--transcript {
	text-align: left;
	font-weight: 300;
}

.left-aligned-grid .c-video--description {
	color: var(--color-gray-600);
}

/* Responsive styles
   ========================================================================== */
/* Mobile Large (480px and up) */
@media (--mobile-large) {
	.c-video--headline {
		font-size: var(--size-font-6xl);
	}
}

/* Tablet (768px and up) */
@media (--tablet) {
	.left-aligned-grid .c-video--headline {
		font-size: var(--size-font-8xl);
	}

	.left-aligned-grid .c-video--transcript {
		font-size: var(--size-font-xl);
	}
}

/* Accessibility
   ========================================================================== */
@media (prefers-reduced-motion: reduce) {
	.c-video--loading,
	.c-video--error {
		transition: none;
	}
}
