<!-- movingimages 24 -->
<div id="{{componentId}}" class="c-video l-grid l-grid--left-s l-grid--center-m {{~#if componentMargin}} component-margin{{/if}} {{~#unless fullWidthVideo}}{{#if largeVideo}} component-wide{{else}} component-small{{/if}}{{/unless}}" data-advanced-interactions-type="{{dataAdvancedInteractionsType}}"
data-analytics="{{json-stringify analyticsData}}"
{{#if headline}}aria-labelledby="video-title-{{componentId}}"{{/if}}
{{#if description}}aria-describedby="video-desc-{{componentId}}"{{/if}}>
  <div class="l-grid l-grid--w-100pc-s l-grid--w-100pc-m {{~#unless isInAccordion}} l-grid--center-s l-grid--center-m{{/unless}}" itemprop="video" itemscope itemtype="http://schema.org/VideoObject">
    {{#if headline}}
      {{#if isInAccordion}}
        <h4 class="l-grid--left-s l-grid--w-100pc-s l-grid--center-m l-grid--w-100pc-m c-video--headline"
            id="video-title-{{componentId}}">
          <span itemprop="name">{{headline}}</span>
        </h4>
      {{else}}
        <h3 class="l-grid--left-s l-grid--w-100pc-s l-grid--center-m l-grid--w-100pc-m c-video--headline"
            id="video-title-{{componentId}}">
          <span itemprop="name">{{headline}}</span>
        </h3>
      {{/if}}
    {{/if}}
    <meta itemprop="transcript" content="transcript" />
    {{#if copy}}
      <div class="l-grid--left-s l-grid--w-100pc-s has-rte c-video--transcript {{#if isInAccordion}}c-video--transcript-small{{/if}}"
           id="video-transcript-{{componentId}}">
        <span itemprop="transcript">
          <p>{{copy}}</p>
        </span>
      </div>
    {{/if}}
    <div id="{{id}}" dir="ltr" class="l-grid--center-s l-grid--w-100pc-s l-grid--center-m l-grid--w-100pc-m c-video--player js--video-player"
         data-video-id="{{videoID}}"
         data-player-id="{{playerID}}"
         {{~#if autoplay}} data-autoplay="{{autoplay}}"{{~/if}}
         role="figure"
         aria-label="{{#if headline}}{{headline}}{{else}}{{a11y.videoPlayerLabel}}{{/if}}"
         {{#if description}}aria-describedby="video-desc-{{componentId}}"{{/if}}
         tabindex="0">

      <div class="c-video--error js--video-error"
           role="alert"
           aria-hidden="true"
           aria-live="assertive">
        <span class="c-video--error-text">{{a11y.errorMessage}}</span>
      </div>

			<div class="c-video--loading js--video-loading" aria-hidden="true">
				<div class="c-video--loading-spinner"></div>
			</div>
    </div>
    {{#if description}}
      <div class="l-grid--left-s l-grid--w-100pc-s has-rte c-video--description"
           id="video-desc-{{componentId}}">
        <span itemprop="description">
          <p>{{description}}</p>
        </span>
      </div>
    {{/if}}
    <meta itemprop="uploadDate" content="{{uploadDate}}" />
    <meta itemprop="duration" content="{{duration}}" />
    {{#if thumbnailUrl}}
      <meta itemprop="thumbnailUrl" content="{{thumbnailUrl}}" />
    {{/if}}
  </div>
</div>
