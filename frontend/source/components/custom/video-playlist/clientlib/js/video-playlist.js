import '../css/video-playlist.css';
import {
	cleanupVideoPlayer,
	ensureIframeAccessibilityWithRetry,
	initializeVideoSDK,
	isInView,
	safelyPauseVideo,
	showErrorState,
	toggleLoading
} from 'utils/mi-sdk-wrapper';
import {
	handlePlaylistSelection,
	setTrackingParameters,
	setupVideoAnalytics
} from '../../../video-mi24pro/clientlib/js/video-analytics';
import {
	isOSMobile, keyValues, mediaQueries
} from 'utils/main';

// Selectors
const VIDEO_PLAYLIST_SELECTOR = '.js--video-playlist';
const VIDEO_TITLE_SELECTOR = '.js--video-player-title';
const VIDEO_PLAYLIST_DESCRIPTION_SELECTOR = '.js--video-description';
const VIDEO_PLAYLIST_PLAYER_MORE_LESS_BUTTON_SELECTOR = '.js--video-player-more-less-button';
const VIDEO_PLAYLIST_PLAYER_SELECTOR = '.js--video-player';
const VIDEO_PLAYLIST_LIST_CONTAINER_SELECTOR = '.js--video-list-container';
const VIDEO_PLAYLIST_LIST_ENTRY_SELECTOR = '.js--video-list-entry';
const VIDEO_PLAYLIST_LIST_TITLE = '.js--video-list-title';

// Attributes
const DATA_MORE_TEXT = 'data-more-text';
const DATA_LESS_TEXT = 'data-less-text';
const DATA_MORE_LESS_STATE = 'data-more-less-state';
const DATA_VIDEO_ID = 'data-video-id';
const DATA_PLAYER_ID = 'data-player-id';
const DATA_ANALYTICS_ATTRIBUTE = 'data-analytics';
const ARIA_EXPANDED = 'aria-expanded';
const ARIA_CONTROLS = 'aria-controls';
const ARIA_LABEL = 'aria-label';

// Variables with specific values to be used later on
const heightOfPlaylistOnMobileViewport = '20.5rem';
const heightOfPlaylistOnTabletDesktopViewport = '34rem';

// The list "componentInstances" will contain the instances for all "Video Playlist components
const componentInstances = [];
// The list "containerElements" will contain all "Video Playlist elements
const containerElements = document.querySelectorAll(VIDEO_PLAYLIST_SELECTOR);

class Video {
	constructor (playlistEntry, videoId, playerId, videoTitle) {
		this.playlistEntry = playlistEntry || {};
		this.videoId = videoId;
		this.playerId = playerId;
		this.videoTitle = videoTitle;
		this.playlistEntry.videoInstance = this;
		this.payload = this.playlistEntry?.getAttribute(DATA_ANALYTICS_ATTRIBUTE);
		this.playerContainer = this.playlistEntry?.closest(VIDEO_PLAYLIST_SELECTOR)?.querySelector(VIDEO_PLAYLIST_PLAYER_SELECTOR);
		this.player = null;
	}

	/**
	 * Read the corresponding value of the attribute [data-analytics] and
	 * initialize for video element properties that maintain
	 * different values based on user interactions with the video.
	 */
	initializePropertiesForVideo () {
		try {
			this.playlistEntry.parsedJsonPayload = JSON.parse(this.payload);
		}
		catch (error) {
			console.error('[UTF] ==> JSON.parse() of [data-analytics] failed:', error);
		}
	}

	/**
	 * Initialize the player using the MovingImage SDK
	 *
	 * @returns {Promise<Object>} The initialized player instance
	 */
	async initializePlayer () {
		// Show loading state
		toggleLoading(this.playerContainer, true);

		// Update ARIA label and data attributes
		this.playerContainer.setAttribute('aria-label', this.videoTitle || 'Video player');

		// Update data attributes to match the new video
		this.playerContainer.setAttribute('data-video-id', this.videoId);
		this.playerContainer.setAttribute('data-player-id', this.playerId);

		// Copy the data-analytics attribute to the player container so analytics work properly
		if (this.payload) {
			this.playerContainer.setAttribute(DATA_ANALYTICS_ATTRIBUTE, this.payload);
		}

		const config = {
			videoId: this.videoId,
			playerId: this.playerId,
			container: this.playerContainer,
			autoplay: false
		};

		try {
			// First check if there are any leftover iframes or video elements
			const existingMedia = this.playerContainer.querySelectorAll('iframe, video');

			if (existingMedia.length > 0) {
				existingMedia.forEach((element) => element.remove());
			}

			// Use the centralized SDK initializer
			this.player = await initializeVideoSDK(config);

			// Setup analytics using the centralized video-analytics.js
			setupVideoAnalytics(this.player, this.playerContainer);

			// Use the centralized ensure iframe accessibility functions with retry mechanism
			ensureIframeAccessibilityWithRetry(this.playerContainer);

			return this.player;
		}
		catch (error) {
			console.error('[Video Playlist] Failed to initialize player:', error);
			toggleLoading(this.playerContainer, false);
			throw error;
		}
	}

	/**
	 * Configure video data and initialize player
	 *
	 * @returns {Promise<void>} A promise that resolves when configuration is complete
	 */
	async configureVideoData () {
		try {
			// Initialize properties for video
			this.initializePropertiesForVideo();

			// Initialize the player
			await this.initializePlayer();

			// Set tracking parameters
			setTrackingParameters(this.videoId, this.playerId);
		}
		catch (error) {
			console.error('[Video Playlist] Failed to configure video data:', error);
			throw error;
		}
	}

	/**
	 * Deregister events and cleanup
	 *
	 * @returns {Promise<void>} A promise that resolves when cleanup is complete
	 */
	async deregisterEvents () {
		try {
			await cleanupVideoPlayer(this.player, this.playerContainer);
			this.player = null;
		}
		catch (error) {
			console.error('[Video Playlist] Failed to deregister events:', error);
			throw error;
		}
	}
}

class VideoPlaylist {
	constructor (videoPlaylistComponent) {
		this.videoPlaylistComponent = videoPlaylistComponent;
		this.currentVideoIndex = 0;
		this.activeVideoInstance = null;
		this.preloadedPlayers = new Map();

		// Initialize the component
		this.setupPlayerContainer();
		this.setupListContainer();
		this.setupShowMoreLessButton();
		this.setupTitleAndDescriptionVisibility();
		this.addResizeAndScrollListeners();

		// Initialize the first video automatically
		this.initializeFirstVideo();
	}

	/**
	 * Setup the player container
	 */
	setupPlayerContainer () {
		this.playerContainer = this.videoPlaylistComponent.querySelector(VIDEO_PLAYLIST_PLAYER_SELECTOR);
	}

	/**
	 * Setup the list container and add event listeners to the list entries
	 */
	setupListContainer () {
		this.listContainer = this.videoPlaylistComponent.querySelector(VIDEO_PLAYLIST_LIST_CONTAINER_SELECTOR);
		this.listEntries = this.videoPlaylistComponent.querySelectorAll(VIDEO_PLAYLIST_LIST_ENTRY_SELECTOR);

		this.setupListEntries();
	}

	/**
	 * Initialize the first video in the playlist
	 */
	initializeFirstVideo () {
		// Get the first entry in the playlist
		const firstEntry = this.listEntries[0];

		if (!firstEntry) {
			return;
		}

		// Get video details
		const videoId = firstEntry.getAttribute(DATA_VIDEO_ID);
		const playerId = this.playerContainer.getAttribute(DATA_PLAYER_ID);
		const videoTitle = firstEntry.querySelector(VIDEO_PLAYLIST_LIST_TITLE)?.textContent || 'Video player';

		// Create the Video instance for the first entry
		this.activeVideoInstance = new Video(firstEntry, videoId, playerId, videoTitle);

		// Configure the video data which will also set up analytics
		this.activeVideoInstance.configureVideoData().catch((error) => {
			console.error('[Video Playlist] Failed to initialize first video:', error);
		});
	}

	/**
	 * Set up the list entries for keyboard navigation
	 */
	setupListEntries () {
		this.listEntries = this.videoPlaylistComponent.querySelectorAll(VIDEO_PLAYLIST_LIST_ENTRY_SELECTOR);

		// Set up keyboard navigation for the list entries
		const options = this.listContainer.querySelectorAll('[role="option"]');

		// Make only the first option tabbable initially
		options.forEach((option, index) => {
			option.setAttribute('tabindex', index === 0 ? '0' : '-1');

			// Add keydown event listener for tab navigation
			option.addEventListener('keydown', (e) => {
				// Handle tab navigation
				if (e.key === 'Tab') {
					// If shift+tab on first item, let the browser handle it
					if (e.shiftKey && option === options[0]) {
						return;
					}

					// If tab on last item, let the browser handle it
					if (!e.shiftKey && option === options[options.length - 1]) {
						return;
					}

					e.preventDefault();

					// Calculate the next/previous option
					const currentIndex = Array.from(options).indexOf(option);
					const nextIndex = e.shiftKey ? currentIndex - 1 : currentIndex + 1;

					// Update tabindex and focus
					options.forEach((opt) => opt.setAttribute('tabindex', '-1'));
					options[nextIndex].setAttribute('tabindex', '0');
					options[nextIndex].focus();
				}
			});
		});

		this.listContainer.addEventListener('click', this.onListEntryClick.bind(this));
		this.listContainer.addEventListener('keydown', this.onListEntryKeyDown.bind(this));
	}

	/**
	 * Setup the "Show more" / "Show less" button and add event listeners
	 */
	setupShowMoreLessButton () {
		this.moreText = this.videoPlaylistComponent.getAttribute(DATA_MORE_TEXT);
		this.lessText = this.videoPlaylistComponent.getAttribute(DATA_LESS_TEXT);
		this.buttonShowMoreAriaLabel = this.videoPlaylistComponent.getAttribute('data-show-more-text-aria-label');
		this.buttonShowLessAriaLabel = this.videoPlaylistComponent.getAttribute('data-show-less-text-aria-label');

		this.showMoreLessButton = this.videoPlaylistComponent.querySelector(VIDEO_PLAYLIST_PLAYER_MORE_LESS_BUTTON_SELECTOR);

		if (this.showMoreLessButton) {
			this.showMoreLessButton.addEventListener('click', this.onMoreLessButtonClick.bind(this));
			this.showMoreLessButton.addEventListener('keyup', this.onMoreLessButtonClick.bind(this));
		}
	}

	/**
	 * Setup the visibility of the title and description based on the viewport size
	 */
	setupTitleAndDescriptionVisibility () {
		this.title = this.videoPlaylistComponent.querySelector(VIDEO_TITLE_SELECTOR);
		this.description = this.videoPlaylistComponent.querySelector(VIDEO_PLAYLIST_DESCRIPTION_SELECTOR);
		this.isVisible = window.matchMedia(mediaQueries.DESKTOP).matches;
		this.handleVisibilityOfDescriptionAndMoreLessButton(this.isVisible);
	}

	/**
	 * Add event listeners for window resize and scroll events
	 */
	addResizeAndScrollListeners () {
		window.addEventListener('resize', this.onResize.bind(this));
		window.addEventListener('scroll', this.onScroll.bind(this));
	}

	/**
	 * Handle window resize event
	 */
	onResize () {
		setTimeout(() => {
			if (window.matchMedia(mediaQueries.DESKTOP).matches) {
				this.listContainer.style.height = `${this.playerContainer.offsetHeight / 10}rem`;
			}
			else if (window.matchMedia(mediaQueries.TABLET).matches) {
				this.listContainer.style.height = heightOfPlaylistOnTabletDesktopViewport;
			}
			else {
				this.listContainer.style.height = heightOfPlaylistOnMobileViewport;
			}
		}, 50);
	}

	/**
	 * Handle the visibility of the description and the "Show more" / "Show less" button
	 *
	 * @param {Boolean} isVisible - Whether the description should be visible
	 */
	handleVisibilityOfDescriptionAndMoreLessButton (isVisible) {
		// Check if description has actual content
		const descriptionContent = this.description?.innerHTML?.trim();

		// If there's no description or the button doesn't exist, just hide the description
		if (!descriptionContent || !this.showMoreLessButton) {
			if (this.description) {
				this.description.classList.add('hidden');
			}

			// Also hide the button if there's no description content
			if (this.showMoreLessButton) {
				this.showMoreLessButton.classList.add('hidden');
			}

			return;
		}

		// Ensure button is visible if description exists
		if (this.showMoreLessButton) {
			this.showMoreLessButton.classList.remove('hidden');
		}

		// If there's a description, set the display style, text and state accordingly
		const newState = isVisible ? 'less' : 'more';
		const newText = isVisible ? this.lessText : this.moreText;

		// Ensure we have valid aria labels
		const showMoreAriaLabel = this.buttonShowMoreAriaLabel || 'Show more';
		const showLessAriaLabel = this.buttonShowLessAriaLabel || 'Show less';
		const newAriaLabel = isVisible ? showLessAriaLabel : showMoreAriaLabel;

		// Toggle description visibility
		if (isVisible) {
			this.description.classList.remove('hidden');
		}
		else {
			this.description.classList.add('hidden');
		}

		// Update button attributes
		this.showMoreLessButton.setAttribute(DATA_MORE_LESS_STATE, newState);
		this.showMoreLessButton.setAttribute(ARIA_LABEL, newAriaLabel);
		this.showMoreLessButton.innerText = newText || (isVisible ? 'Show less' : 'Show more');
		this.showMoreLessButton.setAttribute(ARIA_EXPANDED, isVisible.toString());
	}

	/**
	 * Handle click on the "Show more" / "Show less" button
	 *
	 * @param {Event} event - The click or keyup event
	 */
	onMoreLessButtonClick (event) {
		if (event.type === 'click' || event.key === keyValues.SPACE || event.key === keyValues.ENTER) {
			event.preventDefault();

			// Toggle visibility state
			this.isVisible = !this.isVisible;

			// Defocus and refocus the button for mobile screen readers
			if (isOSMobile) {
				this.showMoreLessButton.blur();
				setTimeout(() => {
					this.showMoreLessButton.focus();
				}, 10);
			}

			this.handleVisibilityOfDescriptionAndMoreLessButton(this.isVisible);
			this.showMoreLessButton.setAttribute(ARIA_EXPANDED, this.isVisible);
		}
	}

	/**
	 * Handle click on a playlist entry
	 *
	 * @param {Event} e - The click event
	 */
	onListEntryClick (e) {
		const option = e.target.closest('[role="option"]');

		if (option) {
			this.selectListEntry(option);
		}
	}

	/**
	 * Handle keydown events on list entries
	 *
	 * @param {Event} e - The keydown event
	 */
	onListEntryKeyDown (e) {
		const option = e.target.closest('[role="option"]');

		if (!option) {
			return;
		}

		// Enter or Space to select
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			this.selectListEntry(option);
		}

		// Arrow keys for navigation
		if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
			e.preventDefault();

			const options = Array.from(this.listContainer.querySelectorAll('[role="option"]'));
			const currentIndex = options.indexOf(option);
			let newIndex;

			if (e.key === 'ArrowDown') {
				newIndex = Math.min(currentIndex + 1, options.length - 1);
			}
			else {
				newIndex = Math.max(currentIndex - 1, 0);
			}

			// Update tabindex and focus
			options.forEach((opt) => opt.setAttribute('tabindex', '-1'));
			options[newIndex].setAttribute('tabindex', '0');
			options[newIndex].focus();
		}
	}

	/**
	 * Select a playlist entry and switch to the corresponding video
	 *
	 * @param {HTMLElement} option - The selected option element
	 */
	async selectListEntry (option) {
		const listEntry = option.closest(VIDEO_PLAYLIST_LIST_ENTRY_SELECTOR);
		const index = Array.from(this.listContainer.querySelectorAll(VIDEO_PLAYLIST_LIST_ENTRY_SELECTOR)).indexOf(listEntry);
		const videoId = listEntry?.getAttribute(DATA_VIDEO_ID);
		const playerId = this.playerContainer.getAttribute(DATA_PLAYER_ID);
		const videoTitle = listEntry.querySelector(VIDEO_PLAYLIST_LIST_TITLE)?.textContent || 'Video player';

		// Check if essential IDs are present
		if (!videoId || !playerId) {
			console.error(`[Video Playlist] Critical Error: Missing videoId (${videoId}) or playerId (${playerId}) for selected entry. Aborting video load.`);

			// Show an error state in the player container immediately
			showErrorState(this.playerContainer, 'Cannot load video: Missing video configuration.');
			toggleLoading(this.playerContainer, false);

			return;
		}

		// Check if we're already playing this video
		if (this.activeVideoInstance && this.activeVideoInstance.videoId === videoId) {
			// If it's the same video, maybe just ensure UI is correct and return
			this.updateUIForNewVideo(listEntry, index, option);

			return;
		}

		// Track playlist selection analytics using the centralized handler
		handlePlaylistSelection(listEntry);

		// Show loading state (now we know we have IDs)
		toggleLoading(this.playerContainer, true);

		// Deregister events and destroy current video instance
		if (this.activeVideoInstance) {
			// Wrap deregister in try/catch for robustness
			await this.activeVideoInstance.deregisterEvents();

			// Clear the previous reference to ensure we don't have stale data
			this.activeVideoInstance = null;
		}

		// Update UI first to provide immediate feedback
		this.updateUIForNewVideo(listEntry, index, option);

		// Create and initialize new video instance
		this.activeVideoInstance = new Video(listEntry, videoId, playerId, videoTitle);

		// Configure the video data which will also set up analytics
		await this.activeVideoInstance.configureVideoData();
	}

	/**
	 * Update the UI when a new video is selected
	 *
	 * @param {HTMLElement} listEntry - The selected list entry
	 * @param {Number} index - The index of the selected entry
	 * @param {HTMLElement} option - The selected option element
	 */
	updateUIForNewVideo (listEntry, index, option) {
		// Update current index
		this.currentVideoIndex = index;

		// Get entry details
		const entryTitle = listEntry.querySelector(VIDEO_PLAYLIST_LIST_TITLE);
		const entryDescription = listEntry.getAttribute('data-description');

		// Update title and description
		this.title.textContent = entryTitle.textContent;

		if (this.description) {
			this.description.innerHTML = entryDescription;
			this.handleVisibilityOfDescriptionAndMoreLessButton(this.isVisible);
		}

		// Update active state
		this.listContainer
			.querySelector('.is-active-video')
			?.classList.remove('is-active-video');
		listEntry.classList.add('is-active-video');

		// Update ARIA attributes
		const options = this.listContainer.querySelectorAll('[role="option"]');

		options.forEach((opt) => {
			opt.setAttribute('aria-selected', 'false');
			opt.setAttribute('tabindex', '-1');
		});

		option.setAttribute('aria-selected', 'true');
		option.setAttribute('tabindex', '0');

		// Update controls - Add null check for showMoreLessButton
		if (this.showMoreLessButton) {
			const contentID = listEntry.getAttribute('data-video-id');

			this.showMoreLessButton.setAttribute(ARIA_CONTROLS, contentID);
		}

		// Initialize video element
		initializeVideoElement(listEntry);

		// Wait for iframe to be created and ensure it has title using the centralized function
		ensureIframeAccessibilityWithRetry(this.playerContainer);
	}

	/**
	 * Handle scroll event to pause video when not in view
	 */
	onScroll () {
		if (!this.activeVideoInstance?.player || !this.playerContainer) {
			return;
		}

		// Check if the player container is in view
		if (!inTheView(this.playerContainer)) {
			safelyPauseVideo(this.activeVideoInstance.player);
		}
	}

	/**
	 * Preload the next video in the playlist
	 *
	 * @param {Number} currentIndex - The index of the current video
	 */
	async preloadNextVideo (currentIndex) {
		const nextEntry = this.listEntries[currentIndex + 1];

		if (
			!nextEntry ||
			this.preloadedPlayers.has(nextEntry.getAttribute(DATA_VIDEO_ID))
		) {
			return;
		}

		const videoId = nextEntry.getAttribute(DATA_VIDEO_ID);
		const playerId = this.playerContainer.getAttribute(DATA_PLAYER_ID);

		try {
			// Create a hidden container for preloading and ensure it stays in the DOM
			const preloadContainer = document.createElement('div');
			const containerId = generateUniqueId();

			preloadContainer.id = containerId;

			// Append to the player container instead of body to maintain context
			preloadContainer.style.position = 'absolute';
			preloadContainer.style.visibility = 'hidden';
			preloadContainer.style.width = '0';
			preloadContainer.style.height = '0';
			preloadContainer.style.overflow = 'hidden';
			this.playerContainer.appendChild(preloadContainer);

			const {
				Player
			} = await import('https://e.video-cdn.net/mi-player-sdk/mi-player-sdk.js');
			const player = new Player(containerId, {
				videoId,
				playerId,
				container: preloadContainer
			});

			// Store the preloaded player
			this.preloadedPlayers.set(videoId, {
				player,
				container: preloadContainer
			});

			// Add error handler to clean up on failure
			player.on('error', () => {
				preloadContainer.remove();
				this.preloadedPlayers.delete(videoId);
			});
		}
		catch (error) {
			console.error('[Video Playlist] Failed to preload video:', error);
		}
	}

	/**
	 * Clean up preloaded players except for the current one
	 *
	 * @param {String} currentVideoId - The ID of the current video
	 */
	cleanupPreloadedPlayers (currentVideoId) {
		for (const [
			videoId,
			{
				player, container
			}
		] of this.preloadedPlayers.entries()) {
			if (videoId !== currentVideoId) {
				player.destroy?.();
				container.remove();
				this.preloadedPlayers.delete(videoId);
			}
		}
	}
}

/**
 * Initialize the video element and add event listeners
 *
 * @param {HTMLElement} listElement - The list entry element
 */
const initializeVideoElement = (listElement) => {
	if (!listElement) {
		return;
	}

	const videoId = listElement.getAttribute(DATA_VIDEO_ID);
	const playerId = listElement.closest(VIDEO_PLAYLIST_SELECTOR)?.querySelector(VIDEO_PLAYLIST_PLAYER_SELECTOR)?.getAttribute(DATA_PLAYER_ID);
	const videoTitle = listElement.querySelector(VIDEO_PLAYLIST_LIST_TITLE)?.textContent || 'Video player';

	// Create a new Video instance and configure it
	const videoInstance = new Video(listElement, videoId, playerId, videoTitle);

	videoInstance.configureVideoData();
};

/**
 * Checks whether the video is in the viewport
 *
 * @private
 * @param {HTMLVideoElement} video - DOM element
 * @returns {Boolean} Whether the video is in the current viewport or not
 */
const inTheView = (video) => isInView(video);

/**
 * Set up all video playlist components on the page
 */
const setup = () => {
	containerElements.forEach((containerElement) => {
		componentInstances.push(new VideoPlaylist(containerElement));
	});
};

/**
 * Initialize the component
 */
const initialize = () => {
	// Run setup when the DOM is loaded
	document.addEventListener('DOMContentLoaded', setup);
};

/**
 * Generate a unique ID for preload containers
 *
 * @returns {String} A unique identifier
 */
const generateUniqueId = () => `video-preload-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

export {
	initialize as default
};
