.is-editmode .c-modal {
	visibility: visible;
}

/* Configuration styles */
.c-modal--content-area-padding-0 {
	--padding: 0rem; /* stylelint-disable-line length-zero-no-unit */
}

.c-modal--content-area-padding-14 {
	--padding: 1.4rem;
}

.c-modal--content-area-padding-21 {
	--padding: 2.1rem;
}

.c-modal--content-area-padding-28 {
	--padding: 2.8rem;
}

.c-modal--content-area-padding-35 {
	--padding: 3.5rem;
}

.c-modal--content-area-padding-56 {
	--padding: 5.6rem;
}

/* General modal styles */
.c-modal {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100dvw;
	height: 100dvh;
	border: none;
	z-index: 1111; /* this z-index is putted here just in case we want to overlay navigation */
	transform: translate3d(0, 0, 0); /* to make elements with position: fixed work relatively to modal (https://jira.dhl.com/browse/DPDHLPA-35353) */
	max-width: 100%;
	max-height: 100%;
	padding: var(--grid-col-gap);
	background-color: transparent;
}

.c-modal[data-modal-size="full-screen"] {
	padding-left: 0;
	padding-right: 0;
}

.c-modal[open] {
	transition: opacity 500ms cubic-bezier(0.25, 0, 0.3, 1);
}

.c-modal:not([open]) {
	pointer-events: none;
	opacity: 0;
	display: none;
}

.c-modal[loading] {
	visibility: hidden;
}

.c-modal .c-modal--container {
	overflow: hidden;
	height: fit-content;

	/* Configurable styles */
	width: var(--grid-cols-12);
	min-height: 4.2rem;
	max-height: 100%;
}

.c-modal[data-modal-size="full-screen"] .c-modal--container {
	width: calc(100% - 4.2rem);
}

.c-modal.c-modal--content-area-color-white .c-modal--container,
.c-modal.c-modal--content-area-color-white.has-close-icon-inside .c-modal--close-icon-container {
	background-color: var(--color-white-500);
}

.c-modal.c-modal--content-area-color-black .c-modal--container,
.c-modal.c-modal--content-area-color-black.has-close-icon-inside .c-modal--close-icon-container {
	background-color: var(--color-black-400);
}

.c-modal .c-modal--content {
	min-height: 4.2rem;
	overflow: hidden auto;
	padding: var(--padding);
	max-height: calc(100dvh - 8.4rem - calc(2 * var(--padding, 2.1rem)));
}

.c-modal[data-modal-size="full-screen"] .c-modal--content {
	overflow: hidden;
}

.c-modal.has-close-icon-inside .c-modal--content {
	padding: calc(var(--padding) - 4.2rem) var(--padding) var(--padding) var(--padding);
}

/* Overlay styles */
.c-modal .c-modal--overlay {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: -1;
	opacity: var(--opacity);
	animation: backdrop-fade-in 500ms ease;
}

.c-modal.c-modal--background-opacity-10 .c-modal--overlay {
	--opacity: 0.1;
}

.c-modal.c-modal--background-opacity-50 .c-modal--overlay {
	--opacity: 0.5;
}

.c-modal.c-modal--background-opacity-90 .c-modal--overlay {
	--opacity: 0.9;
}

.c-modal.c-modal--background-opacity-100 .c-modal--overlay {
	--opacity: 1;
}

.c-modal.c-modal--background-color-black .c-modal--overlay {
	background-color: var(--color-black-400);
}

.c-modal.c-modal--background-color-black .c-modal--container {
	box-shadow: 0 0.2rem 0.6rem -0.1rem rgba(0, 0, 0, 0.12), 0 0.7rem 1.7rem 0 rgba(0, 0, 0, 0.12), 0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.12);
}

.c-modal.c-modal--background-color-white .c-modal--overlay {
	background-color: var(--color-white-500);
}

.c-modal.c-modal--background-color-white .c-modal--container {
	box-shadow: 0 0.2rem 0.6rem -0.1rem rgba(0, 0, 0, 0.1), 0 0.7rem 1.7rem 0 rgba(0, 0, 0, 0.1), 0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.1);
}

.c-modal.c-modal--background-color-yellow .c-modal--overlay {
	background-color: var(--color-yellow-500);
}

.c-modal.c-modal--background-color-yellow .c-modal--container {
	box-shadow: 0 0.2rem 0.6rem -0.1rem rgba(242, 144, 5, 0.12), 0 0.7rem 1.7rem 0 rgba(242, 144, 5, 0.12), 0 0.1rem 0.1rem 0 rgba(183, 67, 0, 0.12);
}

.c-modal.c-modal--content-area-color-transparent .c-modal--container {
	background-color: transparent;
	box-shadow: none;
}

/* Closing icon styles */
.c-modal .c-modal--close-icon-container {
	display: flex;
	justify-content: flex-end;
	height: 4.2rem;
}

.c-modal .c-modal--close-icon {
	cursor: pointer;
	font-size: var(--size-font-4xl);
	line-height: var(--size-line-height-xs);
	margin: 0.7rem;
	display: flex;
}

.c-modal:not(.has-close-icon-inside) .c-modal--close-icon {
	top: 0;
	right: 0;
	display: flex;
	position: fixed;
	align-items: center;
}

.c-modal .c-modal--close-icon.c-modal--close-color-black {
	color: var(--color-black-400);
}

.c-modal .c-modal--close-icon.c-modal--close-color-white {
	color: var(--color-white-500);
}

/* Animations */
@keyframes backdrop-fade-in {
	from {
		opacity: 0;
	}

	to {
		opacity: var(--opacity);
	}
}

/* Modal size media queries */
@media (--tablet) {
	.c-modal[data-modal-size="small"],
	.c-modal[data-modal-size="medium"],
	.c-modal[data-modal-size="large"] {
		padding-left: calc(2 * var(--grid-col-gap));
		padding-right: calc(2 * var(--grid-col-gap));
	}

	.c-modal[data-modal-size="small"] .c-modal--container {
		width: var(--grid-cols-7);
	}
}

@media (--desktop) {
	.c-modal[data-modal-size="small"],
	.c-modal[data-modal-size="medium"],
	.c-modal[data-modal-size="large"] {
		padding-left: calc(2 * var(--grid-col-gap));
		padding-right: calc(2 * var(--grid-col-gap));
	}

	.c-modal[data-modal-size="small"] .c-modal--container {
		width: var(--grid-cols-5);
	}
}

@media (--desktop-large) {
	.c-modal[data-modal-size="small"],
	.c-modal[data-modal-size="medium"],
	.c-modal[data-modal-size="large"] {
		padding-left: calc(4 * var(--grid-col-gap));
		padding-right: calc(4 * var(--grid-col-gap));
	}

	.c-modal[data-modal-size="small"] .c-modal--container {
		max-width: calc((var(--width-page-max-nopadding-small-c20) + calc(4 * var(--grid-col-gap))) / 2);
	}

	.c-modal[data-modal-size="medium"] .c-modal--container {
		max-width: var(--width-page-max-nopadding-small-c20);
	}

	.c-modal[data-modal-size="large"] .c-modal--container {
		max-width: var(--width-page-max-nopadding);
	}
}
