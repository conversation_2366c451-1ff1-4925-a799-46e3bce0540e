import '../css/modal.css';

import {
	disableBodyScroll, enableBodyScroll
} from 'utils/body-scroll-lock';
import {
	default as Queue
} from 'utils/queue';
import {
	checkIfFormIsUsedInsideModal
} from 'forms/form-inside-modal-check';
import {
	on as eventOn
} from 'utils/event-delegation';
import {
	default as isEditMode
} from 'utils/is-edit-mode';
import {
	keyValues
} from 'utils/main';
import {
	wrap as throttleWrap
} from 'utils/throttle';

// throttle timeout
let timeout = false;

// throttle delay in ms
const delayMs = 500;

// global modals variable
let modals;

// data attributes
const ATTRS = {
	OPEN: 'open',
	INERT: 'inert',
	LOADING: 'loading',
	MODAL: 'js--modal',
	AUTOFOCUS: 'autofocus',
	SIZE: 'data-modal-size',
	TARGET: 'data-modal-open',
	CLOSE: 'data-modal-close',
	OVERLAY: 'c-modal--overlay',
	ONLOAD: 'data-modal-onload-open',
	DISMISS: 'data-modal-light-dismiss',
	VIDEO_AUTO_PLAY: 'data-video-autoplay'
};

// locators
const LOCATORS = {
	MODAL: `.${ATTRS.MODAL}`,
	CONTENT: '.c-modal--content',
	CONTAINER: '.js--modal--container',
	CLOSE_ICON: '.js--modal--close-icon',
	CLOSE_ICON_CONTAINER: '.js--modal--close-icon-container',
	OPEN_CTA: `[${ATTRS.TARGET}]`,
	CLOSE_CTA: `[${ATTRS.CLOSE}]`,
	AUTOFOCUS: `[${ATTRS.AUTOFOCUS}]`,
	ON_LOAD_MODALS: `[${ATTRS.ONLOAD}="true"]`
};

// actions
const ACTIONS = {
	OPEN_MODAL: 'openModal',
	CLOSE_MODAL: 'closeModal',
	LIGHT_DISMISS: 'lightDismiss'
};

// modal queue init
const modalQueue = new Queue();

// wait for all dialog animations to complete their promises
const animationsComplete = (element) => Promise.allSettled(element.getAnimations().map((animation) => animation.finished));

/**
 * Add modal to the queue.
 *
 * @param {String} id - The modal id.
 */
const addToModalQueue = (id) => {
	modalQueue.enqueue(id);
};

/**
 * Check if modal queue is empty.
 */
const checkIfModalQueueIsEmpty = () => {
	if (!modalQueue.isEmpty) {
		const modal = document.querySelector(`[id="${modalQueue.peek()}"]`);

		if (modal) {
			openModal(modal, true);
		}
	}
};

/**
 * Find the appropriate focus target within the modal
 *
 * @param {HTMLElement} modal - The modal element.
 * @returns {HTMLElement} The element that should receive focus
 */
const findFocusTarget = (modal) => {
	const modalContent = modal.querySelector(LOCATORS.CONTENT);

	// Check for video player first - prioritize video controls for accessibility
	const videoPlayer = modalContent.querySelector('.js--video-player');

	if (videoPlayer) {
		// Focus on the video player container which will allow keyboard navigation to video controls
		return videoPlayer;
	}

	// Check for autofocus elements
	const autofocusElement = modalContent.querySelector(LOCATORS.AUTOFOCUS);

	if (autofocusElement) {
		return autofocusElement;
	}

	// Check for first focusable element in content
	const focusableElements = modalContent.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');

	if (focusableElements.length > 0) {
		return focusableElements[0];
	}

	// Fallback to modal itself
	return modal;
};

/**
 * Opens modal
 *
 * @param {HTMLElement} modal - The modal element.
 * @param {Boolean} fromQueue - If modal is opened from queue.
 */
const openModal = (modal, fromQueue = false) => {
	const modalContent = modal.querySelector(LOCATORS.CONTENT);

	if (!fromQueue) {
		addToModalQueue(modal.id);
	}

	modal.removeAttribute(ATTRS.INERT);
	modal.showModal();
	// native dialog does not have native event for opening, therefor adding custom one
	modal.dispatchEvent(new CustomEvent('open'));
	disableBodyScroll(modalContent);

	adjustModalContainerWidth(modal);

	// Find the appropriate focus target based on modal content
	const focusTarget = findFocusTarget(modal);

	if (focusTarget) {
		// Use setTimeout to ensure the modal is fully rendered before focusing
		setTimeout(() => {
			focusTarget.focus();
		}, 0);
	}
};

/**
 * Close modal.
 *
 * @param {HTMLElement} modal - The modal element.
 */
const closeModal = (modal) => {
	modal.close();
};

/**
 * Light dismiss handler.
 *
 * @param {Event} event - The event object.
 */
const lightDismiss = (event) => {
	const modal = event.target.closest(LOCATORS.MODAL);

	if (modal.getAttribute(ATTRS.DISMISS) === 'true') {
		if (event.target.classList.contains(ATTRS.OVERLAY)) {
			closeModal(modal);
		}
	}
};

/**
 * Check if form is used inside modal.
 *
 * @param {Event} event - The event object.
 */
const disableEscKey = (event) => {
	if (event.key === 'Escape') {
		event.preventDefault();
	}
};

/**
 * Check possibility of using esc key.
 *
 * @param {HTMLElement} modal - The modal element.
 */
const checkEscKeyAvailability = (modal) => {
	const closeIcon = modal.querySelector(LOCATORS.CLOSE_ICON);

	document.removeEventListener('keydown', disableEscKey);

	if (modal.getAttribute(ATTRS.DISMISS) === 'false' && !closeIcon) {
		if (modal.hasAttribute(ATTRS.OPEN)) {
			document.addEventListener('keydown', disableEscKey);
		}
	}
};

/**
 * Adjusts modal container width.
 *
 * @param {HTMLElement} modal - The modal element.
 */
const adjustModalContainerWidth = (modal) => {
	const hasCloseIconInside = modal.classList.contains('has-close-icon-inside');

	if (hasCloseIconInside) {
		const closeIcon = modal.querySelector(LOCATORS.CLOSE_ICON);
		const modalContainer = closeIcon.closest(LOCATORS.CONTAINER);
		const closeIconContainer = closeIcon.closest(LOCATORS.CLOSE_ICON_CONTAINER);

		closeIconContainer.style.width = `${modalContainer.getBoundingClientRect().width / 10}rem`;
	}
};

/**
 * Handling cta click Events
 *
 * @param {Event} event - The event object.
 * @param {Object} options - Event options
 * @param {String} options.action - Action name
 * @returns {Boolean} - Prevents default event behavior
 */
const ctaClickHandler = (event, {
	action
}) => {
	const cta = event.target.tagName === 'BUTTON' ? event.target : event.target.closest(LOCATORS.OPEN_CTA) || event.target.closest(LOCATORS.CLOSE_CTA);
	const modal = document.querySelector(`[id="${cta.getAttribute(ATTRS.TARGET) || cta.getAttribute(ATTRS.CLOSE)}"]`);

	if (event.type === 'keydown' && event.key !== keyValues.ENTER) {
		return;
	}

	event.preventDefault();

	animationsComplete(modal).then(() => {
		switch (action) {
			case ACTIONS.OPEN_MODAL: {
				if (modalQueue.isEmpty) {
					openModal(modal);
				}
				else {
					addToModalQueue(modal.id);
				}

				break;
			}
			case ACTIONS.CLOSE_MODAL: {
				closeModal(modal);
				break;
			}
		}
	});

	return false;
};

/**
 * Add events to the component.
 */
const addEvents = () => {
	eventOn('click', LOCATORS.OPEN_CTA, {
		action: ACTIONS.OPEN_MODAL
	}, ctaClickHandler);

	eventOn('keydown', LOCATORS.OPEN_CTA, {
		action: ACTIONS.OPEN_MODAL
	}, ctaClickHandler);

	eventOn('click', LOCATORS.CLOSE_CTA, {
		action: ACTIONS.CLOSE_MODAL
	}, ctaClickHandler);

	eventOn('click', LOCATORS.MODAL, {
		action: ACTIONS.LIGHT_DISMISS
	}, lightDismiss);

	window.addEventListener('resize', throttleWrap(() => {
		clearTimeout(timeout);
		timeout = setTimeout(() => window.requestAnimationFrame(() => modals.forEach((modal) => adjustModalContainerWidth(modal))), delayMs);
	}, 100));

	checkIfModalQueueIsEmpty();
};

const initializeSingleModal = (modal) => {
	initialize(modal);
};

/**
 * The component's constructor.
 *
 * @param {Object} singleModal - A single modal
 */
const initialize = (singleModal = null) => {
	if (isEditMode(document)) {
		return;
	}

	checkIfFormIsUsedInsideModal();

	if (singleModal) {
		modals = [
			singleModal
		];
	}
	else {
		modals = document.querySelectorAll(LOCATORS.MODAL);
	}

	if (modals.length) {
		modals.forEach((modal) => {
			if (modal.getAttribute(ATTRS.ONLOAD) === 'true') {
				addToModalQueue(modal.id);
			}

			const modalContent = modal.querySelector(LOCATORS.CONTENT);

			modal.addEventListener('cancel', () => {
				checkEscKeyAvailability(modal);
				enableBodyScroll(modalContent);
			});

			modal.addEventListener('close', () => {
				modalQueue.dequeue();
				modal.setAttribute(ATTRS.INERT, '');
				checkIfModalQueueIsEmpty();
				enableBodyScroll(modalContent);
			});

			modal.removeAttribute(ATTRS.LOADING);
		});

		addEvents();
	}
};

export {
	initialize as default,
	initializeSingleModal,
	addToModalQueue,
	openModal,
	ATTRS,
	LOCATORS
};
