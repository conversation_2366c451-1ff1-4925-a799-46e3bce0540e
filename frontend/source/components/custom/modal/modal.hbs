<dialog id="{{id}}" inert loading aria-label="{{label}}" class="js--flyout-keyboardtrap c-modal js--modal
  {{~#if backgroundColor}} c-modal--background-color-{{backgroundColor}}{{~/if~}}
  {{~#if backgroundOpacity}} c-modal--background-opacity-{{backgroundOpacity}}{{~/if~}}
  {{~#if contentAreaColor}} c-modal--content-area-color-{{contentAreaColor}}{{~/if~}}
  {{~#if contentAreaPadding}} c-modal--content-area-padding-{{contentAreaPadding}}{{~/if~}}
  {{~#if-equals closeIconVisibility 'inside'}} has-close-icon-inside{{/if-equals}}
  "
  {{~#if video}} data-video-autoplay="{{autoplay}}"{{~/if}}
  data-modal-onload-open="{{onload}}" data-modal-light-dismiss="{{lightDismiss}}" data-modal-size="{{size}}" tabindex="-1">
  <div class="c-modal--overlay"></div>
  {{#if-equals closeIconVisibility 'outside'}}
    <button class="c-modal--close-icon js--close-trap js--modal--close-icon has-icon icon-cancel{{~#if ../closeIconColor}} c-modal--close-color-{{../closeIconColor}}{{~/if~}}" data-modal-close="{{../id}}"></button>
  {{/if-equals}}
  <div class="c-modal--container js--modal--container">
    {{#if-equals closeIconVisibility 'inside'}}
      <div class="c-modal--close-icon-container js--modal--close-icon-container">
        <button class="c-modal--close-icon js--modal--close-icon has-icon icon-cancel{{~#if ../closeIconColor}} c-modal--close-color-{{../closeIconColor}}{{~/if~}}" data-modal-close="{{../id}}"></button>
      </div>
    {{/if-equals}}
    <div class="c-modal--content">
      {{~#if @partial-block}}
        {{> @partial-block }}
      {{~/if}}
    </div>
  </div>
</dialog>
